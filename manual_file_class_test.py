#!/usr/bin/env python3
"""
Manual test to create file-class relationships and upload to Neo4j
"""

# Expected file-class relationships for LineageTestProject
expected_relationships = [
    {
        'source_node': 'UserService',
        'source_type': 'File',
        'destination_node': 'UserService',
        'destination_type': 'Class',
        'relationship': 'DECLARES'
    },
    {
        'source_node': 'UserUtils',
        'source_type': 'File',
        'destination_node': 'UserUtils',
        'destination_type': 'Class',
        'relationship': 'DECLARES'
    },
    {
        'source_node': 'OrderService',
        'source_type': 'File',
        'destination_node': 'OrderService',
        'destination_type': 'Class',
        'relationship': 'DECLARES'
    },
    {
        'source_node': 'OrderUtils',
        'source_type': 'File',
        'destination_node': 'OrderUtils',
        'destination_type': 'Class',
        'relationship': 'DECLARES'
    }
]

def create_neo4j_test():
    """Create file-class relationships directly in Neo4j"""
    try:
        from langchain_community.graphs import Neo4jGraph
        
        # Neo4j Configuration
        NEO4J_URI = "bolt://localhost:7687"
        NEO4J_USER = "neo4j"
        NEO4J_PASSWORD = "Test@7889"
        NEO4J_DB = "test"
        
        # Initialize connection
        graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
        
        print("🧪 Manual File-Class Relationship Test")
        print(f"📊 Creating {len(expected_relationships)} relationships...")
        
        # Clear existing data
        graph.query("MATCH (n) DETACH DELETE n")
        print("🗑️ Cleared existing data")
        
        # Create nodes and relationships
        for rel in expected_relationships:
            source_node = rel['source_node']
            dest_node = rel['destination_node']
            source_type = rel['source_type']
            dest_type = rel['destination_type']
            relationship = rel['relationship']
            
            # Create nodes
            graph.query(f"MERGE (f:{source_type} {{name: '{source_node}'}})")
            graph.query(f"MERGE (c:{dest_type} {{name: '{dest_node}'}})")
            
            # Create relationship
            graph.query(f"""
                MATCH (f:{source_type} {{name: '{source_node}'}})
                MATCH (c:{dest_type} {{name: '{dest_node}'}})
                MERGE (f)-[:{relationship}]->(c)
            """)
            
            print(f"✅ Created: {source_node} -[{relationship}]-> {dest_node}")
        
        # Verify results
        result = graph.query("""
            MATCH (f:File)-[r:DECLARES]->(c:Class)
            RETURN f.name as file_name, c.name as class_name, type(r) as relationship
        """)
        
        print(f"\n📋 Verification - Found {len(result)} file-class relationships:")
        for row in result:
            print(f"   {row['file_name']} -[{row['relationship']}]-> {row['class_name']}")
        
        # Check total nodes and relationships
        nodes_result = graph.query("MATCH (n) RETURN labels(n) as labels, count(*) as count")
        rels_result = graph.query("MATCH ()-[r]->() RETURN type(r) as type, count(*) as count")
        
        print(f"\n📊 Database Summary:")
        print("Nodes:")
        for row in nodes_result:
            print(f"   {row['labels'][0]}: {row['count']}")
        print("Relationships:")
        for row in rels_result:
            print(f"   {row['type']}: {row['count']}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = create_neo4j_test()
    if success:
        print("\n✅ Manual test completed successfully!")
        print("🌐 Check Neo4j browser at http://localhost:7474")
        print("🔍 Query: MATCH (f:File)-[r:DECLARES]->(c:Class) RETURN f, r, c")
    else:
        print("\n❌ Manual test failed!")
