source_node,source_type,destination_node,destination_type,relationship,stage
Oneinsights,Folder,Servicesbolt,Folder,CONTAINS,hierarchy
Servicesbolt,Folder,Application,File,CONTAINS,hierarchy
Servicesbolt,Folder,Triggercollector,File,CONTAINS,hierarchy
Servicesbolt,Folder,Api,Folder,CONTAINS,hierarchy
Api,Folder,Almconfigcontroller,File,CONTAINS,hierarchy
Api,Folder,Almcontroller,File,CONTAINS,hierarchy
Servicesbolt,Folder,Request,Folder,CONTAINS,hierarchy
Request,Folder,Almconfigreq,File,CONTAINS,hierarchy
Request,Folder,Almtoolreq,File,CONTAINS,hierarchy
Servicesbolt,Folder,Response,Folder,CONTAINS,hierarchy
Response,Folder,Authentication,File,CONTAINS,hierarchy
Response,Folder,Dataresponse,File,CONTAINS,hierarchy
Servicesbolt,Folder,Service,Folder,CONTAINS,hierarchy
Service,Folder,Almconfigservice,File,CONTAINS,hierarchy
Service,Folder,Almconfigserviceimplementation,File,CONTAINS,hierarchy
Service,Folder,Almservice,File,CONTAINS,hierarchy
Service,Folder,Almserviceimplementation,File,CONTAINS,hierarchy
Service,Folder,Configurationsettingservice,File,CONTAINS,hierarchy
Service,Folder,Configurationsettingserviceimplementation,File,CONTAINS,hierarchy
Servicesbolt,Folder,Util,Folder,CONTAINS,hierarchy
Util,Folder,Dateutil,File,CONTAINS,hierarchy
Oneinsights,Folder,Unifiedbolt,Folder,CONTAINS,hierarchy
Unifiedbolt,Folder,Core,Folder,CONTAINS,hierarchy
Core,Folder,Application,File,CONTAINS,hierarchy
Core,Folder,Constantvariable,File,CONTAINS,hierarchy
Core,Folder,Projectcollector,File,CONTAINS,hierarchy
Core,Folder,Config,Folder,CONTAINS,hierarchy
Config,Folder,Configuration,File,CONTAINS,hierarchy
Config,Folder,Dataconfig,File,CONTAINS,hierarchy
Config,Folder,Mongoaggregate,File,CONTAINS,hierarchy
Core,Folder,Model,Folder,CONTAINS,hierarchy
Model,Folder,Almconfiguration,File,CONTAINS,hierarchy
Model,Folder,Basemodel,File,CONTAINS,hierarchy
Model,Folder,Changehistorymodel,File,CONTAINS,hierarchy
Model,Folder,Componentvelocitylist,File,CONTAINS,hierarchy
Model,Folder,Configurationsetting,File,CONTAINS,hierarchy
Model,Folder,Configurationtoolinfometric,File,CONTAINS,hierarchy
Model,Folder,Customfields,File,CONTAINS,hierarchy
Model,Folder,Iterationmodel,File,CONTAINS,hierarchy
Model,Folder,Iterationoutmodel,File,CONTAINS,hierarchy
Model,Folder,Metricsmodel,File,CONTAINS,hierarchy
Model,Folder,Monogoutmetrics,File,CONTAINS,hierarchy
Model,Folder,Scorecardsprintdata,File,CONTAINS,hierarchy
Model,Folder,Transitionmodel,File,CONTAINS,hierarchy
Model,Folder,Velocitylist,File,CONTAINS,hierarchy
Core,Folder,Repository,Folder,CONTAINS,hierarchy
Repository,Folder,Almconfigrepo,File,CONTAINS,hierarchy
Repository,Folder,Changehisortyrepo,File,CONTAINS,hierarchy
Repository,Folder,Configurationsettingrep,File,CONTAINS,hierarchy
Repository,Folder,Iterationrepo,File,CONTAINS,hierarchy
Repository,Folder,Metricrepo,File,CONTAINS,hierarchy
Repository,Folder,Transitionrepo,File,CONTAINS,hierarchy
Unifiedbolt,Folder,Jira,Folder,CONTAINS,hierarchy
Jira,Folder,Almclientimplementation,File,CONTAINS,hierarchy
Jira,Folder,Chartcalculations,File,CONTAINS,hierarchy
Jira,Folder,Customfieldnames,File,CONTAINS,hierarchy
Jira,Folder,Deletejiraissues,File,CONTAINS,hierarchy
Jira,Folder,Effortandchangeiteminfo,File,CONTAINS,hierarchy
Jira,Folder,Issuehierarchy,File,CONTAINS,hierarchy
Jira,Folder,Iterationinfo,File,CONTAINS,hierarchy
Jira,Folder,Jiraapplication,File,CONTAINS,hierarchy
Jira,Folder,Jiraauthentication,File,CONTAINS,hierarchy
Jira,Folder,Jiraclient,File,CONTAINS,hierarchy
Jira,Folder,Jiraexceptions,File,CONTAINS,hierarchy
Jira,Folder,Metricsinfo,File,CONTAINS,hierarchy
Jira,Folder,Rallyauthentication,File,CONTAINS,hierarchy
Jira,Folder,Releaseinfo,File,CONTAINS,hierarchy
Jira,Folder,Sprintwisecalculation,File,CONTAINS,hierarchy
Jira,Folder,Transitioninfo,File,CONTAINS,hierarchy
Jira,Folder,Transitionmetrices,File,CONTAINS,hierarchy
Unifiedbolt,Folder,Util,Folder,CONTAINS,hierarchy
Util,Folder,Backlogcalculation,File,CONTAINS,hierarchy
Util,Folder,Buildcalculations,File,CONTAINS,hierarchy
Util,Folder,Commonfunctions,File,CONTAINS,hierarchy
Util,Folder,Constant,File,CONTAINS,hierarchy
Util,Folder,Cryptoutils,File,CONTAINS,hierarchy
Util,Folder,Defectcalculations,File,CONTAINS,hierarchy
Util,Folder,Encryptiondecryptionaes,File,CONTAINS,hierarchy
Util,Folder,Encryptoraesgcmpassword,File,CONTAINS,hierarchy
Util,Folder,Restclient,File,CONTAINS,hierarchy
Util,Folder,Sprintprogress,File,CONTAINS,hierarchy
Util,Folder,Sprintprogresscalculations,File,CONTAINS,hierarchy
Util,Folder,Storyprogressmodel,File,CONTAINS,hierarchy
Util,Folder,Storyprogresssprintwise,File,CONTAINS,hierarchy
Util,Folder,Taskrisksprint,File,CONTAINS,hierarchy
Util,Folder,Teamqualityutils,File,CONTAINS,hierarchy
Util,Folder,Velocitycalculations,File,CONTAINS,hierarchy
Almserviceimplementation,File,Transitioncomparator,Class,DECLARES,hierarchy
Almserviceimplementation,File,Sprintcomparatort,Class,DECLARES,hierarchy
Chartcalculations,File,Transitioncomparator,Class,DECLARES,hierarchy
Chartcalculations,File,Sprintcomparatort,Class,DECLARES,hierarchy
Defectcalculations,File,Valuecomparator,Class,DECLARES,hierarchy
Defectcalculations,File,Sprintcomparatort,Class,DECLARES,hierarchy
Sprintprogress,File,Logdatecomparator,Class,DECLARES,hierarchy
Sprintprogress,File,Sprintcomparator,Class,DECLARES,hierarchy
Application,Class,Configure,Method,DECLARES,llm_enhanced
Application,Class,Passwordencoder,Method,DECLARES,llm_enhanced
Application,Class,Main,Method,DECLARES,llm_enhanced
Triggercollector,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Triggercollector,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Triggercollector,Class,Getdatafromtools,Method,DECLARES,llm_enhanced
Getdatafromtools,Method,Ctx,Variable,USES,llm_enhanced
Getdatafromtools,Method,Portfolio,Variable,USES,llm_enhanced
Getdatafromtools,Method,Scheduler,Variable,USES,llm_enhanced
Getdatafromtools,Method,Jobkey,Variable,USES,llm_enhanced
Getdatafromtools,Method,Job,Variable,USES,llm_enhanced
Getdatafromtools,Method,Joba,Variable,USES,llm_enhanced
Getdatafromtools,Method,Trigger,Variable,USES,llm_enhanced
Almconfigcontroller,Class,Log,Variable,HAS_FIELD,llm_enhanced
Almconfigcontroller,Class,Almconfigservice,Variable,HAS_FIELD,llm_enhanced
Almconfigcontroller,Class,Savealmconfig,Method,DECLARES,llm_enhanced
Almconfigcontroller,Class,Retrievelist,Method,DECLARES,llm_enhanced
Almconfigcontroller,Class,Retrievealmconfig,Method,DECLARES,llm_enhanced
Almconfigcontroller,Class,/Almconfig,Endpoint,DECLARES,llm_enhanced
Almconfigcontroller,Class,/Almconfigdetails,Endpoint,DECLARES,llm_enhanced
Almconfigcontroller,Class,/Almconfigdetailsconfig,Endpoint,DECLARES,llm_enhanced
Almcontroller,Class,Service,Variable,HAS_FIELD,llm_enhanced
Almcontroller,Class,Storyageing,Method,DECLARES,llm_enhanced
Almcontroller,Class,Groomingtable,Method,DECLARES,llm_enhanced
Almcontroller,Class,Delduplicate,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getsprintprogresshome,Method,DECLARES,llm_enhanced
Almcontroller,Class,Defectinsightdata,Method,DECLARES,llm_enhanced
Almcontroller,Class,Defecttrendandclassification,Method,DECLARES,llm_enhanced
Almcontroller,Class,Defectclassification,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getissuebrakeup,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getstoryprogress,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdefectssummaryhome,Method,DECLARES,llm_enhanced
Almcontroller,Class,Gettaskriskstorypoint,Method,DECLARES,llm_enhanced
Almcontroller,Class,Burndowncalculation,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getproductionslippage,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdefectdensity,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdefectbacklog,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdefectpareto,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getactivesprints,Method,DECLARES,llm_enhanced
Almcontroller,Class,Delallisues,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getmetricsdatas,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getalltransitions,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getprojectmetrics,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getchangesitems,Method,DECLARES,llm_enhanced
Almcontroller,Class,Gettransitionsdata,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getiterationdata,Method,DECLARES,llm_enhanced
Almcontroller,Class,Geteffortdata,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getprojectdetials,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getcurrentprojectdetials,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getcurrentiter,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getiterations,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdefectcount,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getrelease,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getunreleasedata,Method,DECLARES,llm_enhanced
Getunreleasedata,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getdefects,Method,DECLARES,llm_enhanced
Getdefects,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getsladata,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getassigneeissues,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getdateiterations,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getproddefects,Method,DECLARES,llm_enhanced
Getproddefects,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getalmtype,Method,DECLARES,llm_enhanced
Getalmtype,Method,Almtype,Variable,USES,llm_enhanced
Getalmtype,Method,Config,Variable,USES,llm_enhanced
Getalmtype,Method,Metric,Variable,USES,llm_enhanced
Getalmtype,Method,Configuration1,Variable,USES,llm_enhanced
Getalmtype,Method,Metric1,Variable,USES,llm_enhanced
Almcontroller,Class,Getvelocitychart,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getissuehierarchy,Method,DECLARES,llm_enhanced
Getissuehierarchy,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getcomponentwiseissuehierarchy,Method,DECLARES,llm_enhanced
Getcomponentwiseissuehierarchy,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getcomponentwisevelocitychart,Method,DECLARES,llm_enhanced
Getcomponentwisevelocitychart,Method,Resp,Variable,USES,llm_enhanced
Almcontroller,Class,Getcomponontwisesprintwisestories,Method,DECLARES,llm_enhanced
Getcomponontwisesprintwisestories,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Getcomponents,Method,DECLARES,llm_enhanced
Getcomponents,Method,Response,Variable,USES,llm_enhanced
Almcontroller,Class,Updatecomponent,Method,DECLARES,llm_enhanced
Almcontroller,Class,Saveengscore,Method,DECLARES,llm_enhanced
Almcontroller,Class,Getfeaturemetrics,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Storyname,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Priorityname,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Defectname,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Releasename,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Taskname,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Closestate,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Newstate,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Progressstate,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Criticalpriority,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Highpriority,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Medpriority,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Lowpriority,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Tracksset,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Rejectionphase,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Reopenphase,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Testingphase,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Productionphase,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Personhours,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Timezone,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Velocityfields,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Environment,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Safeenabled,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Ccrlabel,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Cycletimestates,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Throughputstates,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Firstsprint,Variable,HAS_FIELD,llm_enhanced
Almconfigreq,Class,Getccrlabel,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setccrlabel,Method,DECLARES,llm_enhanced
Setccrlabel,Method,Ccrlabel,Variable,USES,llm_enhanced
Almconfigreq,Class,Getcycletimestates,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setcycletimestates,Method,DECLARES,llm_enhanced
Setcycletimestates,Method,Cycletimestates,Variable,USES,llm_enhanced
Almconfigreq,Class,Getthroughputstates,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setthroughputstates,Method,DECLARES,llm_enhanced
Setthroughputstates,Method,Throughputstates,Variable,USES,llm_enhanced
Almconfigreq,Class,Getrejectionphase,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setrejectionphase,Method,DECLARES,llm_enhanced
Setrejectionphase,Method,Rejectionphase,Variable,USES,llm_enhanced
Almconfigreq,Class,Getreopenphase,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setreopenphase,Method,DECLARES,llm_enhanced
Setreopenphase,Method,Reopenphase,Variable,USES,llm_enhanced
Almconfigreq,Class,Gettestingphase,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Settestingphase,Method,DECLARES,llm_enhanced
Settestingphase,Method,Testingphase,Variable,USES,llm_enhanced
Almconfigreq,Class,Getproductionphase,Method,DECLARES,llm_enhanced
Almconfigreq,Class,Setproductionphase,Method,DECLARES,llm_enhanced
Setproductionphase,Method,Productionphase,Variable,USES,llm_enhanced
Almtoolreq,Class,Almtype,Variable,HAS_FIELD,llm_enhanced
Almtoolreq,Class,Getalmtype,Method,DECLARES,llm_enhanced
Almtoolreq,Class,Setalmtype,Method,DECLARES,llm_enhanced
Setalmtype,Method,Almtype,Variable,USES,llm_enhanced
Authentication,Class,Authntication,Variable,HAS_FIELD,llm_enhanced
Authentication,Class,Authenticationofservice,Method,DECLARES,llm_enhanced
Auth,Class,Response,Variable,HAS_FIELD,llm_enhanced
Auth,Class,Authenticationstatus,Variable,HAS_FIELD,llm_enhanced
Auth,Class,Authenticate,Method,DECLARES,llm_enhanced
Authenticate,Method,Feature,Variable,USES,llm_enhanced
Authenticate,Method,Client,Variable,USES,llm_enhanced
Authenticate,Method,Webtarget,Variable,USES,llm_enhanced
Authenticate,Method,Invocationbuilder,Variable,USES,llm_enhanced
Dataresponse,Class,Lastupdated,Variable,HAS_FIELD,llm_enhanced
Dataresponse,Class,Getresult,Method,DECLARES,llm_enhanced
Dataresponse,Class,Getlastupdated,Method,DECLARES,llm_enhanced
Almconfigservice,Interface,Savealmconfig,Method,DECLARES,llm_enhanced
Almconfigservice,Interface,Retrievealmconfig,Method,DECLARES,llm_enhanced
Almconfigserviceimplementation,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Almconfigserviceimplementation,Class,Log,Variable,HAS_FIELD,llm_enhanced
Almconfigserviceimplementation,Class,Savealmconfig,Method,DECLARES,llm_enhanced
Almconfigserviceimplementation,Class,Retrievealmconfig,Method,DECLARES,llm_enhanced
Retrievealmconfig,Method,Lastupdate,Variable,USES,llm_enhanced
Almservice,Interface,Getmetricdetails,Method,DECLARES,llm_enhanced
Almservice,Interface,Getallmetrics,Method,DECLARES,llm_enhanced
Almservice,Interface,Getchangesitems,Method,DECLARES,llm_enhanced
Almservice,Interface,Gettransitionsdata,Method,DECLARES,llm_enhanced
Almservice,Interface,Getiterationdata,Method,DECLARES,llm_enhanced
Almservice,Interface,Geteffortdata,Method,DECLARES,llm_enhanced
Almservice,Interface,Getprojectdetails,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectcounts,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcrtitr,Method,DECLARES,llm_enhanced
Almservice,Interface,Getrelease,Method,DECLARES,llm_enhanced
Almservice,Interface,Getunreleasedata,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefects,Method,DECLARES,llm_enhanced
Almservice,Interface,Getsladata,Method,DECLARES,llm_enhanced
Almservice,Interface,Getassigneeissues,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdateiterations,Method,DECLARES,llm_enhanced
Almservice,Interface,Getproddefects,Method,DECLARES,llm_enhanced
Almservice,Interface,Delduplicate,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcurrentprojectdetails,Method,DECLARES,llm_enhanced
Almservice,Interface,Delallissues,Method,DECLARES,llm_enhanced
Almservice,Interface,Getalltransitions,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentvelocity,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentssprint,Method,DECLARES,llm_enhanced
Almservice,Interface,Getissuehierarchy,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentwiseissuehierarchy,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponents,Method,DECLARES,llm_enhanced
Almservice,Interface,Updatecomponentsoftaskandsubtask,Method,DECLARES,llm_enhanced
Almservice,Interface,Getfeaturemetrics,Method,DECLARES,llm_enhanced
Almservice,Interface,Getsprintprogresshome,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectssummaryhome,Method,DECLARES,llm_enhanced
Almservice,Interface,Gettaskrisk,Method,DECLARES,llm_enhanced
Almservice,Interface,Getactivesprints,Method,DECLARES,llm_enhanced
Almservice,Interface,Getissuebrakeup,Method,DECLARES,llm_enhanced
Almservice,Interface,Getstoryprogress,Method,DECLARES,llm_enhanced
Almservice,Interface,Burndowncalculation,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectinsightdata,Method,DECLARES,llm_enhanced
Almservice,Interface,Defectparetocalculation,Method,DECLARES,llm_enhanced
Almservice,Interface,Getproductionslippage,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectdensity,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectbacklog,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefecttrendandclassification,Method,DECLARES,llm_enhanced
Almservice,Interface,Getstoryageingdata,Method,DECLARES,llm_enhanced
Almservice,Interface,Getgroomingtable,Method,DECLARES,llm_enhanced
Almservice,Interface,Getalliterations,Method,DECLARES,llm_enhanced
Almservice,Interface,Getdefectclassification,Method,DECLARES,llm_enhanced
Almservice,Interface,Saveengscore,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentvelocitychart,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentssprintstories,Method,DECLARES,llm_enhanced
Almservice,Interface,Getissuehierarchychart,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentwiseissuehierarchychart,Method,DECLARES,llm_enhanced
Almservice,Interface,Getcomponentschart,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Transitionrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Efforthistoryrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Changehisortyrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Authorrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Projectrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Releaserepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Iterationrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Agg,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Mongotemplate,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Workingbacklog,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Workingsprints,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Velocityfields,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Closestates,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Tasknames,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Vlist,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Issuelist,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Widarr,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Tempsprefined,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Tempspremoved,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Finalstoriescommited,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Storiescompleted,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Defetcscompleted,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Refinedissulist,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Removedissulist,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Chartservice,Variable,HAS_FIELD,llm_enhanced
Almserviceimplementation,Class,Getmetricdetails,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getallmetrics,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getchangesitems,Method,DECLARES,llm_enhanced
Transitioncomparator,Class,Compare,Method,DECLARES,llm_enhanced
Sprintcomparatort,Class,Compare,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Gettransitionsdata,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getiterationdata,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Geteffortdata,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getprojectdetails,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getfeaturemetrics,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Populateauthor,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcrtitr,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getdefectcounts,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getrelease,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getunreleasedata,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getdefects,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getproddefects,Method,DECLARES,llm_enhanced
Getprojectdetails,Method,Almty,Variable,USES,llm_enhanced
Populateauthor,Method,Datamodel,Variable,USES,llm_enhanced
Almserviceimplementation,Class,Getdateiterations,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getsladata,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getassigneeissues,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Delduplicate,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcurrentprojectdetails,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Delallissues,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getalltransitions,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentvelocity,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentssprint,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getissuehierarchy,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentlist,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentwiseissuehierarchy,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponents,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getsprintwisestories,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Filtertrans,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getvelocitychart,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Callsp,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Calcclosedsp,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Storyloop,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Storylooprefined,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getgroomingtable,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getalliterations,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getalmtype,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Saveengscore,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentvelocitychart,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentssprintstories,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getissuehierarchychart,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentwiseissuehierarchychart,Method,DECLARES,llm_enhanced
Almserviceimplementation,Class,Getcomponentschart,Method,DECLARES,llm_enhanced
Saveengscore,Method,Engscrmap,Variable,USES,llm_enhanced
Saveengscore,Method,Logger,Variable,USES,llm_enhanced
Getcomponentvelocitychart,Method,Logger,Variable,USES,llm_enhanced
Getcomponentvelocitychart,Method,Chartservice,Variable,USES,llm_enhanced
Getcomponentssprintstories,Method,Logger,Variable,USES,llm_enhanced
Getcomponentssprintstories,Method,Chartservice,Variable,USES,llm_enhanced
Getissuehierarchychart,Method,Chartservice,Variable,USES,llm_enhanced
Getcomponentwiseissuehierarchychart,Method,Logger,Variable,USES,llm_enhanced
Getcomponentwiseissuehierarchychart,Method,Chartservice,Variable,USES,llm_enhanced
Getcomponentschart,Method,Chartservice,Variable,USES,llm_enhanced
Configurationsettingservice,Interface,Getconfig,Method,DECLARES,llm_enhanced
Configurationsettingservice,Interface,Addconfig,Method,DECLARES,llm_enhanced
Configurationsettingservice,Interface,Deleteconfig,Method,DECLARES,llm_enhanced
Configurationsettingservice,Interface,Deleteallcollections,Method,DECLARES,llm_enhanced
Configurationsettingservice,Interface,Deleteproject,Method,DECLARES,llm_enhanced
Configurationsettingservice,Interface,Getconfigproject,Method,DECLARES,llm_enhanced
Configurationsettingserviceimplementation,Class,Configurationsettingrepository,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Almservice,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Buildrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Buildfailurepatternrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Codecoveragerepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Codequalityrep,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Healthrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Scmrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Chartconfigrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Goalsettingrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Portfoliorepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Projecthealthrepo,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Userassociation,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Log,Variable,HAS_FIELD,llm_enhanced
Configurationsettingserviceimplementation,Class,Getconfig,Method,DECLARES,llm_enhanced
Getconfig,Method,Lastupdated,Variable,USES,llm_enhanced
Configurationsettingserviceimplementation,Class,Addconfig,Method,DECLARES,llm_enhanced
Addconfig,Method,Date,Variable,USES,llm_enhanced
Addconfig,Method,Timestamp,Variable,USES,llm_enhanced
Configurationsettingserviceimplementation,Class,Deleteconfig,Method,DECLARES,llm_enhanced
Configurationsettingserviceimplementation,Class,Deleteallcollections,Method,DECLARES,llm_enhanced
Deleteallcollections,Method,Failurepattern,Variable,USES,llm_enhanced
Deleteallcollections,Method,Failurepatternobj,Variable,USES,llm_enhanced
Configurationsettingserviceimplementation,Class,Deleteproject,Method,DECLARES,llm_enhanced
Configurationsettingserviceimplementation,Class,Getconfigproject,Method,DECLARES,llm_enhanced
Getconfigproject,Method,Config,Variable,USES,llm_enhanced
Dateutil,Class,Getdateinformat,Method,DECLARES,llm_enhanced
Getdateinformat,Method,Pattern,Variable,USES,llm_enhanced
Getdateinformat,Method,Format,Variable,USES,llm_enhanced
Getdateinformat,Method,Simpledateformat,Variable,USES,llm_enhanced
Dateutil,Class,Getlastweekworkingdaterange,Method,DECLARES,llm_enhanced
Getlastweekworkingdaterange,Method,Date,Variable,USES,llm_enhanced
Getlastweekworkingdaterange,Method,Start,Variable,USES,llm_enhanced
Getlastweekworkingdaterange,Method,End,Variable,USES,llm_enhanced
Constantvariable,Class,Name,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Date,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Version,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Msr,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Formatted_Value,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Key,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Alert_Text,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Date_Format,Variable,HAS_FIELD,llm_enhanced
Constantvariable,Class,Jobs_Url_For_Db,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Getprojectname,Method,DECLARES,llm_enhanced
Projectcollector,Class,Setprojectname,Method,DECLARES,llm_enhanced
Projectcollector,Class,Printmessage,Method,DECLARES,llm_enhanced
Projectcollector,Class,Multitaskthread,Method,DECLARES,llm_enhanced
Projectcollector,Class,Makeswitchcasecall,Method,DECLARES,llm_enhanced
Projectcollector,Class,Githubaction,Method,DECLARES,llm_enhanced
Projectcollector,Class,Servicenow,Method,DECLARES,llm_enhanced
Projectcollector,Class,Azureboard,Method,DECLARES,llm_enhanced
Projectcollector,Class,Azurerepo,Method,DECLARES,llm_enhanced
Projectcollector,Class,Azurecc,Method,DECLARES,llm_enhanced
Projectcollector,Class,Highlight,Method,DECLARES,llm_enhanced
Projectcollector,Class,Gitlabcodecoverage,Method,DECLARES,llm_enhanced
Projectcollector,Class,Execute,Method,DECLARES,llm_enhanced
Projectcollector,Class,Test,Method,DECLARES,llm_enhanced
Projectcollector,Class,Bitbucketpipeline,Method,DECLARES,llm_enhanced
Projectcollector,Class,Gitlabpipeline,Method,DECLARES,llm_enhanced
Projectcollector,Class,Smarttestdefect,Method,DECLARES,llm_enhanced
Projectcollector,Class,Smarttest,Method,DECLARES,llm_enhanced
Projectcollector,Class,Hpalm,Method,DECLARES,llm_enhanced
Projectcollector,Class,Projecthealth,Method,DECLARES,llm_enhanced
Projectcollector,Class,Sprintcomparison,Method,DECLARES,llm_enhanced
Projectcollector,Class,Circleci,Method,DECLARES,llm_enhanced
Projectcollector,Class,Jenkins,Method,DECLARES,llm_enhanced
Projectcollector,Class,Jenkinspipeline,Method,DECLARES,llm_enhanced
Projectcollector,Class,Jira,Method,DECLARES,llm_enhanced
Projectcollector,Class,Engscorecard,Method,DECLARES,llm_enhanced
Projectcollector,Class,Sonarqube,Method,DECLARES,llm_enhanced
Projectcollector,Class,Bit,Method,DECLARES,llm_enhanced
Projectcollector,Class,Codecoverage,Method,DECLARES,llm_enhanced
Projectcollector,Class,Junit,Method,DECLARES,llm_enhanced
Projectcollector,Class,Svn,Method,DECLARES,llm_enhanced
Projectcollector,Class,Git,Method,DECLARES,llm_enhanced
Projectcollector,Class,Teamcity,Method,DECLARES,llm_enhanced
Projectcollector,Class,Tfsbuild,Method,DECLARES,llm_enhanced
Projectcollector,Class,Tfsversion,Method,DECLARES,llm_enhanced
Projectcollector,Class,Bitserver,Method,DECLARES,llm_enhanced
Projectcollector,Class,Gitlab,Method,DECLARES,llm_enhanced
Projectcollector,Class,Codeclimate,Method,DECLARES,llm_enhanced
Projectcollector,Class,Octopusdeploy,Method,DECLARES,llm_enhanced
Projectcollector,Class,Sendmail,Method,DECLARES,llm_enhanced
Projectcollector,Class,Getsmtpinfo,Method,DECLARES,llm_enhanced
Projectcollector,Class,Destroy,Method,DECLARES,llm_enhanced
Projectcollector,Class,Evictallcaches,Method,DECLARES,llm_enhanced
Projectcollector,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Executor,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Threadgroup,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Childthreadgroup,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Log,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Mailsent,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Metric1,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Toaddress,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Msgbody,Variable,HAS_FIELD,llm_enhanced
Projectcollector,Class,Subject,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Host,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Db,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Userid,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Password,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Port,Variable,HAS_FIELD,llm_enhanced
Configuration,Class,Secret,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Log,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Myobj,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Mongotemplate,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Host,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Port,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Dbuserid,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Db,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Dbpassword,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Secret,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Client,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,In,Variable,HAS_FIELD,llm_enhanced
Dataconfig,Class,Getinstance,Method,DECLARES,llm_enhanced
Getinstance,Method,Myobj,Variable,USES,llm_enhanced
Dataconfig,Class,Getdatabasename,Method,DECLARES,llm_enhanced
Dataconfig,Class,Mongo,Method,DECLARES,llm_enhanced
Mongo,Method,Properties,Variable,USES,llm_enhanced
Mongo,Method,In,Variable,USES,llm_enhanced
Mongo,Method,Host,Variable,USES,llm_enhanced
Mongo,Method,Port,Variable,USES,llm_enhanced
Mongo,Method,Dbuserid,Variable,USES,llm_enhanced
Mongo,Method,Dbpassword,Variable,USES,llm_enhanced
Mongo,Method,Secret,Variable,USES,llm_enhanced
Mongo,Method,Db,Variable,USES,llm_enhanced
Mongo,Method,Mongoclientoptions,Variable,USES,llm_enhanced
Mongo,Method,Hosts,Variable,USES,llm_enhanced
Mongo,Method,Ports,Variable,USES,llm_enhanced
Mongo,Method,Replicaslist,Variable,USES,llm_enhanced
Mongo,Method,Serveraddr,Variable,USES,llm_enhanced
Mongo,Method,Mongocredential,Variable,USES,llm_enhanced
Mongo,Method,Client,Variable,USES,llm_enhanced
Dataconfig,Class,Getmappingbasepackage,Method,DECLARES,llm_enhanced
Dataconfig,Class,Mongotemplate,Method,DECLARES,llm_enhanced
Dataconfig,Class,Getcontext,Method,DECLARES,llm_enhanced
Getcontext,Method,Ctx,Variable,USES,llm_enhanced
Mongoaggregate,Class,Template,Variable,HAS_FIELD,llm_enhanced
Mongoaggregate,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Mongoaggregate,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Mongoaggregate,Class,Efforthistoryrepo,Variable,HAS_FIELD,llm_enhanced
Mongoaggregate,Class,Aggregatemetrics,Method,DECLARES,llm_enhanced
Aggregatemetrics,Method,Metricrepo,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Transitionrepo,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Efforthistoryrepo,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Iterationrepo,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Metriclist,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Transitionlist,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Transitiongrouped,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Efforthistorylist,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Effortsgrouped,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Metricitr,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Aggregatedmetriclist,Variable,USES,llm_enhanced
Aggregatemetrics,Method,Agr,Variable,USES,llm_enhanced
Mongoaggregate,Class,Aggregate,Method,DECLARES,llm_enhanced
Aggregate,Method,Metricrepo,Variable,USES,llm_enhanced
Aggregate,Method,Transitionrepo,Variable,USES,llm_enhanced
Aggregate,Method,Efforthistoryrepo,Variable,USES,llm_enhanced
Aggregate,Method,Iterationrepo,Variable,USES,llm_enhanced
Aggregate,Method,Metriclist,Variable,USES,llm_enhanced
Aggregate,Method,Transitionlist,Variable,USES,llm_enhanced
Aggregate,Method,Transitiongrouped,Variable,USES,llm_enhanced
Aggregate,Method,Efforthistorylist,Variable,USES,llm_enhanced
Aggregate,Method,Effortsgrouped,Variable,USES,llm_enhanced
Aggregate,Method,Metricitr,Variable,USES,llm_enhanced
Aggregate,Method,Aggregatedmetriclist,Variable,USES,llm_enhanced
Aggregate,Method,Agr,Variable,USES,llm_enhanced
Aggregate,Method,Aggregatedmetriclistgrouped,Variable,USES,llm_enhanced
Aggregate,Method,Iterationlist,Variable,USES,llm_enhanced
Aggregate,Method,Iterationirt,Variable,USES,llm_enhanced
Aggregate,Method,Aggreagation,Variable,USES,llm_enhanced
Aggregate,Method,Iteraiotn,Variable,USES,llm_enhanced
Aggregate,Method,Aggregationitr,Variable,USES,llm_enhanced
Aggregate,Method,Query,Variable,USES,llm_enhanced
Mongoaggregate,Class,Getlookup,Method,DECLARES,llm_enhanced
Getlookup,Method,Opr,Variable,USES,llm_enhanced
Mongoaggregate,Class,Getoutoperation,Method,DECLARES,llm_enhanced
Getoutoperation,Method,Opr,Variable,USES,llm_enhanced
Mongoaggregate,Class,Getmetric,Method,DECLARES,llm_enhanced
Getmetric,Method,Filtersname,Variable,USES,llm_enhanced
Getmetric,Method,Coneffort,Variable,USES,llm_enhanced
Getmetric,Method,Contrans,Variable,USES,llm_enhanced
Getmetric,Method,Mtraggr,Variable,USES,llm_enhanced
Getmetric,Method,Mon,Variable,USES,llm_enhanced
Mongoaggregate,Class,Getcurrentitr,Method,DECLARES,llm_enhanced
Getcurrentitr,Method,Filtersname,Variable,USES,llm_enhanced
Getcurrentitr,Method,Sort,Variable,USES,llm_enhanced
Mongoaggregate,Class,Deleteissues,Method,DECLARES,llm_enhanced
Deleteissues,Method,Query,Variable,USES,llm_enhanced
Mongoaggregate,Class,Gettotalsprintcount,Method,DECLARES,llm_enhanced
Gettotalsprintcount,Method,Query,Variable,USES,llm_enhanced
Mongoaggregate,Class,Getbuildscount,Method,DECLARES,llm_enhanced
Getbuildscount,Method,Query,Variable,USES,llm_enhanced
Mongoaggregate,Class,Updatecomponentfortaskandsubtasks,Method,DECLARES,llm_enhanced
Updatecomponentfortaskandsubtasks,Method,Allstories,Variable,USES,llm_enhanced
Updatecomponentfortaskandsubtasks,Method,Componentname,Variable,USES,llm_enhanced
Updatecomponentfortaskandsubtasks,Method,Subtasklist,Variable,USES,llm_enhanced
Updatecomponentfortaskandsubtasks,Method,Tasklist,Variable,USES,llm_enhanced
Updatecomponentfortaskandsubtasks,Method,Issue,Variable,USES,llm_enhanced
Almconfiguration,Class,Storyname,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Priorityname,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Defectname,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Releasename,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Taskname,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Closestate,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Progressstate,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Newstate,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Criticalpriority,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Highpriority,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Medpriority,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Lowpriority,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Tracksset,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Rejectionphase,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Reopenphase,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Testingphase,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Productionphase,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Personhours,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Timezone,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Velocityfields,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Environment,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Safeenabled,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Fixedstate,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Ccrlabel,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Cycletimestates,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Throughputstates,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Filteredsprints,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Issuelinktypes,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Firstsprint,Variable,HAS_FIELD,llm_enhanced
Almconfiguration,Class,Getccrlabel,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setccrlabel,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getcycletimestates,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setcycletimestates,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getthroughputstates,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setthroughputstates,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getrejectionphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setrejectionphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getreopenphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setreopenphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Gettestingphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Settestingphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getproductionphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setproductionphase,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getstoryname,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getpriorityname,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setpriorityname,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getreleasename,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setreleasename,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Gettaskname,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Settaskname,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getclosestate,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setclosestate,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Getcriticalpriority,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Setcriticalpriority,Method,DECLARES,llm_enhanced
Almconfiguration,Class,Gethighpriority,Method,DECLARES,llm_enhanced
Basemodel,Class,Id,Variable,HAS_FIELD,llm_enhanced
Basemodel,Class,Getid,Method,DECLARES,llm_enhanced
Basemodel,Class,Setid,Method,DECLARES,llm_enhanced
Setid,Method,Id,Variable,USES,llm_enhanced
Changehistorymodel,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Field,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Oldvalue,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Newvalue,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Date,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Changehistorymodel,Class,Getprojkey,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setprojkey,Method,DECLARES,llm_enhanced
Setprojkey,Method,Projkey,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getprojectname,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setprojectname,Method,DECLARES,llm_enhanced
Setprojectname,Method,Pname,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getwid,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setwid,Method,DECLARES,llm_enhanced
Setwid,Method,Wid,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getfield,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setfield,Method,DECLARES,llm_enhanced
Setfield,Method,Field,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getoldvalue,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setoldvalue,Method,DECLARES,llm_enhanced
Setoldvalue,Method,Oldvalue,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getnewvalue,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setnewvalue,Method,DECLARES,llm_enhanced
Setnewvalue,Method,Newvalue,Variable,USES,llm_enhanced
Changehistorymodel,Class,Getdate,Method,DECLARES,llm_enhanced
Changehistorymodel,Class,Setdate,Method,DECLARES,llm_enhanced
Setdate,Method,Date,Variable,USES,llm_enhanced
Changehistorymodel,Class,Changeitems,Table,MAPS_TO,llm_enhanced
Componentvelocitylist,Class,Component,Variable,HAS_FIELD,llm_enhanced
Componentvelocitylist,Class,Velocitylist,Variable,HAS_FIELD,llm_enhanced
Componentvelocitylist,Class,Getcomponent,Method,DECLARES,llm_enhanced
Componentvelocitylist,Class,Setcomponent,Method,DECLARES,llm_enhanced
Setcomponent,Method,Component,Variable,USES,llm_enhanced
Componentvelocitylist,Class,Getvelocitylist,Method,DECLARES,llm_enhanced
Componentvelocitylist,Class,Setvelocitylist,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Timestamp,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Baseline,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Addflag,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Projecttype,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Manualdata,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Metric,Variable,HAS_FIELD,llm_enhanced
Configurationsetting,Class,Ismanualdata,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Setmanualdata,Method,DECLARES,llm_enhanced
Setmanualdata,Method,Manualdata,Variable,USES,llm_enhanced
Configurationsetting,Class,Gettimestamp,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Settimestamp,Method,DECLARES,llm_enhanced
Settimestamp,Method,Timestamp,Variable,USES,llm_enhanced
Configurationsetting,Class,Getprojectname,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Setprojectname,Method,DECLARES,llm_enhanced
Setprojectname,Method,Projectname,Variable,USES,llm_enhanced
Configurationsetting,Class,Getmetrics,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Isaddflag,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Setaddflag,Method,DECLARES,llm_enhanced
Setaddflag,Method,Addflag,Variable,USES,llm_enhanced
Configurationsetting,Class,Isbaseline,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Setbaseline,Method,DECLARES,llm_enhanced
Setbaseline,Method,Baseline,Variable,USES,llm_enhanced
Configurationsetting,Class,Getprojecttype,Method,DECLARES,llm_enhanced
Configurationsetting,Class,Setprojecttype,Method,DECLARES,llm_enhanced
Setprojecttype,Method,Projecttype,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Selected,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Id,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Toolname,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Url,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Username,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Password,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Tooltype,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Widgetname,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Jobname,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Projectcode,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Domain,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Host,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Port,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Dbtype,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Schema,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Reponame,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Secret,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Manualdata,Variable,HAS_FIELD,llm_enhanced
Configurationtoolinfometric,Class,Getid,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setid,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Gettoolname,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Settoolname,Method,DECLARES,llm_enhanced
Settoolname,Method,Toolname,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Geturl,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Seturl,Method,DECLARES,llm_enhanced
Seturl,Method,Url,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getusername,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setusername,Method,DECLARES,llm_enhanced
Setusername,Method,Username,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getpassword,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setpassword,Method,DECLARES,llm_enhanced
Setpassword,Method,Password,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Gettooltype,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Settooltype,Method,DECLARES,llm_enhanced
Settooltype,Method,Tooltype,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getwidgetname,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setwidgetname,Method,DECLARES,llm_enhanced
Setwidgetname,Method,Widgetname,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getjobname,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setjobname,Method,DECLARES,llm_enhanced
Setjobname,Method,Jobname,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getprojectcode,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setprojectcode,Method,DECLARES,llm_enhanced
Setprojectcode,Method,Projectcode,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getselected,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setselected,Method,DECLARES,llm_enhanced
Setselected,Method,Selected,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getdomain,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setdomain,Method,DECLARES,llm_enhanced
Setdomain,Method,Domain,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Gethost,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Sethost,Method,DECLARES,llm_enhanced
Sethost,Method,Host,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getport,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setport,Method,DECLARES,llm_enhanced
Setport,Method,Port,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getdbtype,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setdbtype,Method,DECLARES,llm_enhanced
Setdbtype,Method,Dbtype,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getschema,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setschema,Method,DECLARES,llm_enhanced
Setschema,Method,Schema,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getreponame,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setreponame,Method,DECLARES,llm_enhanced
Setreponame,Method,Reponame,Variable,USES,llm_enhanced
Configurationtoolinfometric,Class,Getsecret,Method,DECLARES,llm_enhanced
Configurationtoolinfometric,Class,Setsecret,Method,DECLARES,llm_enhanced
Setsecret,Method,Secret,Variable,USES,llm_enhanced
Customfields,Class,Name,Variable,HAS_FIELD,llm_enhanced
Customfields,Class,Getname,Method,DECLARES,llm_enhanced
Customfields,Class,Setname,Method,DECLARES,llm_enhanced
Setname,Method,Name,Variable,USES,llm_enhanced
Customfields,Class,Getvalue,Method,DECLARES,llm_enhanced
Customfields,Class,Setvalue,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Timestamp,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Stdate,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Velocity,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Spplanned,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Relname,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Reldate,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Techdebt,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Teamsize,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Ptestcase,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Fadd,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Fdel,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Fmod,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Totdefects,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Totcloseddefects,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Totopendefetct,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Stpersprint,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Buildfailed,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,State,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Palmtype,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Sid,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Completeddate,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Totalstorypoints,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Buildfail,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Iterationmodel,Class,Gettimestamp,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Settimestamp,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Gettotcloseddefects,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Settotcloseddefects,Method,DECLARES,llm_enhanced
Settotcloseddefects,Method,Totcloseddefects,Variable,USES,llm_enhanced
Iterationmodel,Class,Gettotopendefetct,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Settotopendefetct,Method,DECLARES,llm_enhanced
Settotopendefetct,Method,Totopendefetct,Variable,USES,llm_enhanced
Iterationmodel,Class,Settotdefects,Method,DECLARES,llm_enhanced
Settotdefects,Method,Totdefects,Variable,USES,llm_enhanced
Iterationmodel,Class,Getsname,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setsname,Method,DECLARES,llm_enhanced
Setsname,Method,Sname,Variable,USES,llm_enhanced
Iterationmodel,Class,Getstdate,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setstdate,Method,DECLARES,llm_enhanced
Setstdate,Method,Stdate,Variable,USES,llm_enhanced
Iterationmodel,Class,Getenddate,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setenddate,Method,DECLARES,llm_enhanced
Setenddate,Method,Enddate,Variable,USES,llm_enhanced
Iterationmodel,Class,Getspplanned,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setspplanned,Method,DECLARES,llm_enhanced
Setspplanned,Method,Spplanned,Variable,USES,llm_enhanced
Iterationmodel,Class,Getrelname,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setrelname,Method,DECLARES,llm_enhanced
Setrelname,Method,Relname,Variable,USES,llm_enhanced
Iterationmodel,Class,Getreldate,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setreldate,Method,DECLARES,llm_enhanced
Setreldate,Method,Reldate,Variable,USES,llm_enhanced
Iterationmodel,Class,Gettechdebt,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Settechdebt,Method,DECLARES,llm_enhanced
Settechdebt,Method,Techdebt,Variable,USES,llm_enhanced
Iterationmodel,Class,Getteamsize,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setteamsize,Method,DECLARES,llm_enhanced
Setteamsize,Method,Teamsize,Variable,USES,llm_enhanced
Iterationmodel,Class,Getptestcase,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setptestcase,Method,DECLARES,llm_enhanced
Setptestcase,Method,Ptestcase,Variable,USES,llm_enhanced
Iterationmodel,Class,Getfadd,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setfadd,Method,DECLARES,llm_enhanced
Setfadd,Method,Fadd,Variable,USES,llm_enhanced
Iterationmodel,Class,Getfdel,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setfdel,Method,DECLARES,llm_enhanced
Setfdel,Method,Fdel,Variable,USES,llm_enhanced
Iterationmodel,Class,Getfmod,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setfmod,Method,DECLARES,llm_enhanced
Setfmod,Method,Fmod,Variable,USES,llm_enhanced
Iterationmodel,Class,Gettotdefects,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Getstpersprint,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setstpersprint,Method,DECLARES,llm_enhanced
Setstpersprint,Method,Stpersprint,Variable,USES,llm_enhanced
Iterationmodel,Class,Getbuildfailed,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setbuildfailed,Method,DECLARES,llm_enhanced
Setbuildfailed,Method,Buildfailed,Variable,USES,llm_enhanced
Iterationmodel,Class,Getstate,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setstate,Method,DECLARES,llm_enhanced
Setstate,Method,State,Variable,USES,llm_enhanced
Iterationmodel,Class,Getpname,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setpname,Method,DECLARES,llm_enhanced
Setpname,Method,Pname,Variable,USES,llm_enhanced
Iterationmodel,Class,Getpalmtype,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setpalmtype,Method,DECLARES,llm_enhanced
Setpalmtype,Method,Palmtype,Variable,USES,llm_enhanced
Iterationmodel,Class,Getsid,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setsid,Method,DECLARES,llm_enhanced
Setsid,Method,Sid,Variable,USES,llm_enhanced
Iterationmodel,Class,Getcompleteddate,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setcompleteddate,Method,DECLARES,llm_enhanced
Setcompleteddate,Method,Completeddate,Variable,USES,llm_enhanced
Iterationmodel,Class,Gettotalstorypoints,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Settotalstorypoints,Method,DECLARES,llm_enhanced
Settotalstorypoints,Method,Totalstorypoints,Variable,USES,llm_enhanced
Iterationmodel,Class,Getbuildfail,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setbuildfail,Method,DECLARES,llm_enhanced
Setbuildfail,Method,Buildfail,Variable,USES,llm_enhanced
Iterationmodel,Class,Getvelocity,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setvelocity,Method,DECLARES,llm_enhanced
Setvelocity,Method,Velocity,Variable,USES,llm_enhanced
Iterationmodel,Class,Compareto,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Getprojkey,Method,DECLARES,llm_enhanced
Iterationmodel,Class,Setprojkey,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Timestamp,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Stdate,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Velocity,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Spplanned,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Relname,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Reldate,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Techdebt,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Teamsize,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Ptestcase,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Fadd,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Fdel,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Fmod,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Totdefects,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Totcloseddefects,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Totopendefetct,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Stpersprint,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Buildfailed,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,State,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Palmtype,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Sid,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Completeddate,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Totalstorypoints,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Buildfail,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Closedstories,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Metrics,Variable,HAS_FIELD,llm_enhanced
Iterationoutmodel,Class,Getclosedstories,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setclosedstories,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettimestamp,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settimestamp,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getsname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setsname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getstdate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setstdate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getenddate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setenddate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getvelocity,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getspplanned,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setspplanned,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getreldate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setreldate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettotcloseddefects,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settotcloseddefects,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettotopendefetct,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settotopendefetct,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettotalstorypoints,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settotalstorypoints,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getbuildfail,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setbuildfail,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setvelocity,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settotdefects,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettechdebt,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Settechdebt,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getteamsize,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setteamsize,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getptestcase,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setptestcase,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getfadd,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setfadd,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getfdel,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setfdel,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getfmod,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setfmod,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Gettotdefects,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getstpersprint,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setstpersprint,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getbuildfailed,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setbuildfailed,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getstate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setstate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getpname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setpname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getpalmtype,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setpalmtype,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getsid,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setsid,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getcompleteddate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setcompleteddate,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getmetrics,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setmetrics,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getrelname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setrelname,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Getprojkey,Method,DECLARES,llm_enhanced
Iterationoutmodel,Class,Setprojkey,Method,DECLARES,llm_enhanced
Setclosedstories,Method,Closedstories,Variable,USES,llm_enhanced
Setmetrics,Method,Metrics,Variable,USES,llm_enhanced
Metricsmodel,Class,Actest,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Affectedversions,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Allocateddate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Assgnto,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Baseline,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Components,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Createdate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Cycletime,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Defectinjector,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Donedate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Effort,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Epiclink,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Estchange,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Exteffort,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Fixver,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Inwardissuelink,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Leadtime,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Orgest,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Outwardissuelink,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Epicissues,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Palmtype,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Priority,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Remtime,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Resdate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Severity,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Sid,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Multisprints,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,State,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Stateset,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Statuscategory,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Storypoints,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Subtasklist,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Summ,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Tasklist,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Type,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Updateddate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Upddate,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Waittime,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Targetrelease,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Url,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Squads,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Category,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Whenfound,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Wherefound,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Howfound,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Environment,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Acfeatureorcapability,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Acfeatureid,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Rallyrefurl,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Customfields,Variable,HAS_FIELD,llm_enhanced
Metricsmodel,Class,Getactest,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getaffectedversions,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getallocateddate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getassgnto,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getbaseline,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getcomponents,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getcreatedate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getcycletime,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getdefectinjector,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getdonedate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Geteffort,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getepiclink,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getestchange,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getexteffort,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getfixver,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getinwardissuelink,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getleadtime,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getorgest,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getoutwardissuelink,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getpalmtype,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getpname,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getpriority,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getremtime,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getresdate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getseverity,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getsid,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getsname,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getstate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getstateset,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getstatuscategory,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getstorypoints,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getsubtasklist,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getsumm,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Gettasklist,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Gettype,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getupdateddate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getupddate,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getwaittime,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getwid,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Gettargetrelease,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getsquads,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getcategory,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getwhenfound,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getwherefound,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Gethowfound,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getepicissues,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getprojkey,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getmultisprints,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getenvironment,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getacfeatureorcapability,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Geturl,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getacfeatureid,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getrallyrefurl,Method,DECLARES,llm_enhanced
Metricsmodel,Class,Getcustomfields,Method,DECLARES,llm_enhanced
Setactest,Method,Actest,Variable,USES,llm_enhanced
Setaffectedversions,Method,Affectedversions,Variable,USES,llm_enhanced
Setallocateddate,Method,Allocateddate,Variable,USES,llm_enhanced
Setassgnto,Method,Assgnto,Variable,USES,llm_enhanced
Setcomponents,Method,Components,Variable,USES,llm_enhanced
Setcreatedate,Method,Createdate,Variable,USES,llm_enhanced
Setcycletime,Method,Cycletime,Variable,USES,llm_enhanced
Setdefectinjector,Method,Defectinjector,Variable,USES,llm_enhanced
Setdonedate,Method,Donedate,Variable,USES,llm_enhanced
Seteffort,Method,Effort,Variable,USES,llm_enhanced
Setepiclink,Method,Epiclink,Variable,USES,llm_enhanced
Setestchange,Method,Estchange,Variable,USES,llm_enhanced
Setexteffort,Method,Exteffort,Variable,USES,llm_enhanced
Setfixver,Method,Fixver,Variable,USES,llm_enhanced
Setinwardissuelink,Method,Inwardissuelink,Variable,USES,llm_enhanced
Setleadtime,Method,Leadtime,Variable,USES,llm_enhanced
Setorgest,Method,Orgest,Variable,USES,llm_enhanced
Setoutwardissuelink,Method,Outwardissuelink,Variable,USES,llm_enhanced
Setpriority,Method,Priority,Variable,USES,llm_enhanced
Setremtime,Method,Remtime,Variable,USES,llm_enhanced
Setresdate,Method,Resdate,Variable,USES,llm_enhanced
Setseverity,Method,Severity,Variable,USES,llm_enhanced
Setstateset,Method,Stateset,Variable,USES,llm_enhanced
Setstatuscategory,Method,Statuscategory,Variable,USES,llm_enhanced
Setstorypoints,Method,Storypoints,Variable,USES,llm_enhanced
Setsubtasklist,Method,Subtasklist,Variable,USES,llm_enhanced
Setsumm,Method,Summ,Variable,USES,llm_enhanced
Settasklist,Method,Tasklist,Variable,USES,llm_enhanced
Settype,Method,Type,Variable,USES,llm_enhanced
Setupdateddate,Method,Updateddate,Variable,USES,llm_enhanced
Setupddate,Method,Upddate,Variable,USES,llm_enhanced
Setwaittime,Method,Waittime,Variable,USES,llm_enhanced
Settargetrelease,Method,Targetrelease,Variable,USES,llm_enhanced
Setsquads,Method,Squads,Variable,USES,llm_enhanced
Setcategory,Method,Category,Variable,USES,llm_enhanced
Setwhenfound,Method,Whenfound,Variable,USES,llm_enhanced
Setwherefound,Method,Wherefound,Variable,USES,llm_enhanced
Sethowfound,Method,Howfound,Variable,USES,llm_enhanced
Setepicissues,Method,Epicissues,Variable,USES,llm_enhanced
Setmultisprints,Method,Multisprints,Variable,USES,llm_enhanced
Setenvironment,Method,Environment,Variable,USES,llm_enhanced
Setacfeatureorcapability,Method,Acfeatureorcapability,Variable,USES,llm_enhanced
Setacfeatureid,Method,Acfeatureid,Variable,USES,llm_enhanced
Setrallyrefurl,Method,Rallyrefurl,Variable,USES,llm_enhanced
Setcustomfields,Method,Customfields,Variable,USES,llm_enhanced
Monogoutmetrics,Class,Actest,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Affectedversions,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Allocateddate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Assgnto,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Baseline,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Components,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Createdate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Cycletime,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Defectinjector,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Donedate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Effort,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Efforts,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Epiclink,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Estchange,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Exteffort,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Fixver,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Targetrelease,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Inwardissuelink,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Leadtime,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Orgest,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Outwardissuelink,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Epicissues,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Palmtype,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Priority,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Remtime,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Resdate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Severity,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Sid,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Multisprints,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,State,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Stateset,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Statuscategory,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Storypoints,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Subtasklist,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Summ,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Tasklist,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Transitions,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Type,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Updateddate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Upddate,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Waittime,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Squads,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Category,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Whenfound,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Wherefound,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Howfound,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Environment,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Acfeatureorcapability,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Acfeatureid,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Rallyrefurl,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Customfields,Variable,HAS_FIELD,llm_enhanced
Monogoutmetrics,Class,Getcustomfields,Method,DECLARES,llm_enhanced
Monogoutmetrics,Class,Setcustomfields,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Sprintname,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Sprintid,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Commitedsp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Completedsp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Commitedaftersp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Spilloversp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Refinedsp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Removedsp,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Startdate,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Effort,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Defects,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Capacity,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Committedstories,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Completedstories,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Refineddefects,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Completeddefects,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,State,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuescommited,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuescommitedafter,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuescomplted,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuesremoved,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuesrefined,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Issuespillover,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Originalestimate,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Timelogged,Variable,HAS_FIELD,llm_enhanced
Scorecardsprintdata,Class,Getoriginalestimate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setoriginalestimate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Gettimelogged,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Settimelogged,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcommittedstories,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcommittedstories,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcompletedstories,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcompletedstories,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Geteffort,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Seteffort,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getdefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setdefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcapacity,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcapacity,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcompleteddefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcompleteddefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getstartdate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setstartdate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getenddate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setenddate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcommitedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcommitedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcompletedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcompletedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getcommitedaftersp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setcommitedaftersp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getrefinedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setrefinedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getremovedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setremovedsp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getsprintname,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setsprintname,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getsprintid,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setsprintid,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getrefineddefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setrefineddefects,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getstate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setstate,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getspilloversp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setspilloversp,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuescommited,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuescommited,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuescommitedafter,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuescommitedafter,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuescomplted,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuescomplted,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuesremoved,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuesremoved,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuesrefined,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuesrefined,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Getissuespillover,Method,DECLARES,llm_enhanced
Scorecardsprintdata,Class,Setissuespillover,Method,DECLARES,llm_enhanced
Setoriginalestimate,Method,Originalestimate,Variable,USES,llm_enhanced
Settimelogged,Method,Timelogged,Variable,USES,llm_enhanced
Setcommittedstories,Method,Committedstories,Variable,USES,llm_enhanced
Setcompletedstories,Method,Completedstories,Variable,USES,llm_enhanced
Setdefects,Method,Defects,Variable,USES,llm_enhanced
Setcapacity,Method,Capacity,Variable,USES,llm_enhanced
Setcompleteddefects,Method,Completeddefects,Variable,USES,llm_enhanced
Setstartdate,Method,Startdate,Variable,USES,llm_enhanced
Setcommitedsp,Method,Commitedsp,Variable,USES,llm_enhanced
Setcompletedsp,Method,Completedsp,Variable,USES,llm_enhanced
Setcommitedaftersp,Method,Commitedaftersp,Variable,USES,llm_enhanced
Setrefinedsp,Method,Refinedsp,Variable,USES,llm_enhanced
Setremovedsp,Method,Removedsp,Variable,USES,llm_enhanced
Setsprintname,Method,Sprintname,Variable,USES,llm_enhanced
Setsprintid,Method,Sprintid,Variable,USES,llm_enhanced
Setrefineddefects,Method,Refineddefects,Variable,USES,llm_enhanced
Setspilloversp,Method,Spilloversp,Variable,USES,llm_enhanced
Setissuescommited,Method,Issuescommited,Variable,USES,llm_enhanced
Setissuescommitedafter,Method,Issuescommitedafter,Variable,USES,llm_enhanced
Setissuescomplted,Method,Issuescomplted,Variable,USES,llm_enhanced
Setissuesremoved,Method,Issuesremoved,Variable,USES,llm_enhanced
Setissuesrefined,Method,Issuesrefined,Variable,USES,llm_enhanced
Setissuespillover,Method,Issuespillover,Variable,USES,llm_enhanced
Transitionmodel,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Crstate,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Frmstate,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Mdfdate,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Waittime,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Prestatewaittime,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Createtime,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Effort,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Leadtime,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Transitionmodel,Class,Getwid,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setwid,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getprestatewaittime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setprestatewaittime,Method,DECLARES,llm_enhanced
Setprestatewaittime,Method,Prestatewaittime,Variable,USES,llm_enhanced
Transitionmodel,Class,Getcreatetime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setcreatetime,Method,DECLARES,llm_enhanced
Setcreatetime,Method,Createtime,Variable,USES,llm_enhanced
Transitionmodel,Class,Geteffort,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Seteffort,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getleadtime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setleadtime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getcrstate,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setcrstate,Method,DECLARES,llm_enhanced
Setcrstate,Method,Crstate,Variable,USES,llm_enhanced
Transitionmodel,Class,Getfrmstate,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setfrmstate,Method,DECLARES,llm_enhanced
Setfrmstate,Method,Frmstate,Variable,USES,llm_enhanced
Transitionmodel,Class,Getmdfdate,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setmdfdate,Method,DECLARES,llm_enhanced
Setmdfdate,Method,Mdfdate,Variable,USES,llm_enhanced
Transitionmodel,Class,Getwaittime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setwaittime,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getpname,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setpname,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getsname,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setsname,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Getprojkey,Method,DECLARES,llm_enhanced
Transitionmodel,Class,Setprojkey,Method,DECLARES,llm_enhanced
Velocitylist,Class,Chartdatacommitted,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdataafter,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdatacompleted,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdatafinalcommited,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdataremoved,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdataeffort,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdatadefects,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartdatacapacity,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartissuescomp,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartissuescomm,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartissuescommafter,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartissuesrefined,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Chartissuesremoved,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Catagories,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Finalcommitedstories,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Finalcommiteddefetcs,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Completedstories,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Completeddefects,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Startdate,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Workitemarr,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Activesprint,Variable,HAS_FIELD,llm_enhanced
Velocitylist,Class,Getworkitemarr,Method,DECLARES,llm_enhanced
Velocitylist,Class,Setworkitemarr,Method,DECLARES,llm_enhanced
Setworkitemarr,Method,Workitemarr,Variable,USES,llm_enhanced
Almconfigrepo,Interface,Findbyprojectname,Method,DECLARES,llm_enhanced
Almconfigrepo,Interface,Deletebyprojectname,Method,DECLARES,llm_enhanced
Changehisortyrepo,Interface,Findbywid,Method,DECLARES,llm_enhanced
Changehisortyrepo,Interface,Findbypname,Method,DECLARES,llm_enhanced
Configurationsettingrep,Interface,Findbyprojectname,Method,DECLARES,llm_enhanced
Configurationsettingrep,Interface,Findbyprojectnamein,Method,DECLARES,llm_enhanced
Configurationsettingrep,Interface,Deletebyprojectname,Method,DECLARES,llm_enhanced
Iterationrepo,Interface,Findbysnameandpname,Method,DECLARES,llm_enhanced
Iterationrepo,Interface,Findbysnameandpnameandpalmtype,Method,DECLARES,llm_enhanced
Iterationrepo,Interface,Findbypname,Method,DECLARES,llm_enhanced
Iterationrepo,Interface,Findbysidandpname,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandsname,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbywidandpnameandsname,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbywid,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandsid,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandtypeandpalmtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandsnameandpalmtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandsidandpalmtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandsnameandtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypname,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandbaselineandtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandwid,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbypnameandpalmtype,Method,DECLARES,llm_enhanced
Metricrepo,Interface,Findbywidandpname,Method,DECLARES,llm_enhanced
Transitionrepo,Interface,Findbypnameandsname,Method,DECLARES,llm_enhanced
Transitionrepo,Interface,Findbywid,Method,DECLARES,llm_enhanced
Transitionrepo,Interface,Findbypname,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Almtype,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Auth,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Changehisortyrepo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Changelogs,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Portfolioviewconfig,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Createddate,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Creationtime,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Efforthistoryrepo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Effortinfo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Efforts,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Fieldsofjsondata,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Historyjsonoutput,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Historyarray,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Iteration,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Iterationinfo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Iterationrepo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Metrics,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Metricsinfo,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Origest,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Almclientimplementation,Class,Getalmtooldata,Method,DECLARES,llm_enhanced
Getalmtooldata,Method,Projectname,Variable,USES,llm_enhanced
Almclientimplementation,Class,Deleteiterations,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Sortiterationdata,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Init,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Getsortedjson,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Populatemetrics,Method,DECLARES,llm_enhanced
Populatemetrics,Method,Portfolioviewconfig,Variable,USES,llm_enhanced
Populatemetrics,Method,Featureid,Variable,USES,llm_enhanced
Almclientimplementation,Class,Saveiteration,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Savemetrics,Method,DECLARES,llm_enhanced
Almclientimplementation,Class,Setvariables,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Workingbacklog,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Workingsprints,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Velocityfields,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Closestates,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Tasknames,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Vlist,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Issuelist,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Widarr,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Tempsprefined,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Tempspremoved,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Finalstoriescommited,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Storiescompleted,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Defetcscompleted,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Refinedissulist,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Removedissulist,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Transitionrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Efforthistoryrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Changehisortyrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Authorrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Projectrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Releaserepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Iterationrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Agg,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Mongotemplate,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Chartcalculations,Class,Getcomponentvelocity,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getcomponentssprintstories,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getissuehierarchy,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getcomponentlist,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getcomponentwiseissuehierarchy,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getcomponents,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getsprintwisestories,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Filtertrans,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Getvelocitychart,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Callsp,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Calcclosedsp,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Storyloop,Method,DECLARES,llm_enhanced
Chartcalculations,Class,Storylooprefined,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Storypoints,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Targetrelease,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Component,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Fixversions,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Releasedate,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Modules,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Affectedversions,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Clientjiraid,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Defectseverity,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Baselinerequirement,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Jira_Sprint_Field_Brillio,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Jira_Field_Defect_Injector,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Target_Release,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Jira_Field_Defect_Type,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Kywrd_Status,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Taskstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Subtaskstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Storystatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Bugstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Epicstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Teststatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Newfeaturelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Improvementstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Bugexternalstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Newfeatureexternalstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Improvementexternalstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Taskexternalstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Subtaskexternalstatelist,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Defectsquads,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Defectcategory,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Whenfound,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Wherefound,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Howfound,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Environment,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Environmetkey,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Priority_Level,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Acfeature_Capability,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Url,Variable,HAS_FIELD,llm_enhanced
Customfieldnames,Class,Getcustomfieldsinfo,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getprojectstatus,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getbugstatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getstatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getepicstatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getstorystatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getsubtaskstatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getteststatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Gettaskstatelist,Method,DECLARES,llm_enhanced
Customfieldnames,Class,Getcustomfieldsoflpm,Method,DECLARES,llm_enhanced
Deletejiraissues,Class,Handledeletedissues,Method,DECLARES,llm_enhanced
Deletejiraissues,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Auth,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Mongoagr,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Widlist,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Metrics,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Metricsrepo,Variable,HAS_FIELD,llm_enhanced
Deletejiraissues,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Handledeletedissues,Method,Ctx,Variable,USES,llm_enhanced
Handledeletedissues,Method,Metricsrepo,Variable,USES,llm_enhanced
Handledeletedissues,Method,Allmetrics,Variable,USES,llm_enhanced
Handledeletedissues,Method,Groupbywid,Variable,USES,llm_enhanced
Handledeletedissues,Method,Jsonoutput,Variable,USES,llm_enhanced
Handledeletedissues,Method,Totalissues,Variable,USES,llm_enhanced
Handledeletedissues,Method,Startat,Variable,USES,llm_enhanced
Handledeletedissues,Method,Maxresults,Variable,USES,llm_enhanced
Handledeletedissues,Method,Jsonarrayoutput,Variable,USES,llm_enhanced
Handledeletedissues,Method,Metrics,Variable,USES,llm_enhanced
Handledeletedissues,Method,Jsonnew,Variable,USES,llm_enhanced
Handledeletedissues,Method,Wid,Variable,USES,llm_enhanced
Handledeletedissues,Method,Tempmetrics,Variable,USES,llm_enhanced
Handledeletedissues,Method,Recentmodel,Variable,USES,llm_enhanced
Handledeletedissues,Method,Mongoagr,Variable,USES,llm_enhanced
Effortandchangeiteminfo,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Effortandchangeiteminfo,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Effortandchangeiteminfo,Class,Populatesubarrays,Method,DECLARES,llm_enhanced
Populatesubarrays,Method,Pname,Variable,USES,llm_enhanced
Populatesubarrays,Method,Sname,Variable,USES,llm_enhanced
Issuehierarchy,Class,Allmetrics,Variable,HAS_FIELD,llm_enhanced
Issuehierarchy,Class,Mappeddata,Variable,HAS_FIELD,llm_enhanced
Issuehierarchy,Class,Unmapped,Variable,HAS_FIELD,llm_enhanced
Issuehierarchy,Class,Gethierarchy,Method,DECLARES,llm_enhanced
Gethierarchy,Method,Tempcopy,Variable,USES,llm_enhanced
Gethierarchy,Method,Tempissue,Variable,USES,llm_enhanced
Gethierarchy,Method,Epiclinks,Variable,USES,llm_enhanced
Gethierarchy,Method,Epics,Variable,USES,llm_enhanced
Gethierarchy,Method,Story,Variable,USES,llm_enhanced
Gethierarchy,Method,Unmapped,Variable,USES,llm_enhanced
Gethierarchy,Method,Child,Variable,USES,llm_enhanced
Gethierarchy,Method,Storydata,Variable,USES,llm_enhanced
Gethierarchy,Method,Group,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getrelatedtaskinfo,Method,DECLARES,llm_enhanced
Getrelatedtaskinfo,Method,Tempcopy,Variable,USES,llm_enhanced
Getrelatedtaskinfo,Method,Tempissue,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getunmappeddata,Method,DECLARES,llm_enhanced
Getunmappeddata,Method,Unmapped,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getdatainstructure,Method,DECLARES,llm_enhanced
Getdatainstructure,Method,Relateddata,Variable,USES,llm_enhanced
Getdatainstructure,Method,Subtasks,Variable,USES,llm_enhanced
Getdatainstructure,Method,Epiclinks,Variable,USES,llm_enhanced
Getdatainstructure,Method,Childdata,Variable,USES,llm_enhanced
Getdatainstructure,Method,Tempmetrics,Variable,USES,llm_enhanced
Issuehierarchy,Class,Gethierarchydata,Method,DECLARES,llm_enhanced
Gethierarchydata,Method,Allmetrics,Variable,USES,llm_enhanced
Gethierarchydata,Method,Hierarchyview,Variable,USES,llm_enhanced
Gethierarchydata,Method,Unmappedhierarchyview,Variable,USES,llm_enhanced
Gethierarchydata,Method,Hierarchymap,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getdataintreestructure,Method,DECLARES,llm_enhanced
Getdataintreestructure,Method,Hierarchyview,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Subtaklist,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Epiclinks,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Node,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Storyid,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Linksdata,Variable,USES,llm_enhanced
Getdataintreestructure,Method,Children,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getchildren,Method,DECLARES,llm_enhanced
Getchildren,Method,Children,Variable,USES,llm_enhanced
Getchildren,Method,Links,Variable,USES,llm_enhanced
Getchildren,Method,Tempissuelist,Variable,USES,llm_enhanced
Getchildren,Method,Tempissue,Variable,USES,llm_enhanced
Issuehierarchy,Class,Getunmappedintreestructure,Method,DECLARES,llm_enhanced
Getunmappedintreestructure,Method,Nodelist,Variable,USES,llm_enhanced
Getunmappedintreestructure,Method,Node,Variable,USES,llm_enhanced
Getunmappedintreestructure,Method,Children,Variable,USES,llm_enhanced
Iterationinfo,Class,Populateiteration,Method,DECLARES,llm_enhanced
Iterationinfo,Class,Getsprintinfo,Method,DECLARES,llm_enhanced
Iterationinfo,Class,Sprintid,Variable,HAS_FIELD,llm_enhanced
Iterationinfo,Class,Iteration,Variable,HAS_FIELD,llm_enhanced
Iterationinfo,Class,Sprintname,Variable,HAS_FIELD,llm_enhanced
Iterationinfo,Class,Iterationset,Variable,HAS_FIELD,llm_enhanced
Iterationinfo,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Populateiteration,Method,Response,Variable,USES,llm_enhanced
Populateiteration,Method,Sprintset,Variable,USES,llm_enhanced
Populateiteration,Method,Objectlist,Variable,USES,llm_enhanced
Populateiteration,Method,Iteration,Variable,USES,llm_enhanced
Populateiteration,Method,Tempkey,Variable,USES,llm_enhanced
Populateiteration,Method,Sprintjson,Variable,USES,llm_enhanced
Populateiteration,Method,Sprintstatus,Variable,USES,llm_enhanced
Populateiteration,Method,Startdate,Variable,USES,llm_enhanced
Populateiteration,Method,Enddate,Variable,USES,llm_enhanced
Populateiteration,Method,Completeddate,Variable,USES,llm_enhanced
Populateiteration,Method,Id,Variable,USES,llm_enhanced
Populateiteration,Method,Multiplesprints,Variable,USES,llm_enhanced
Populateiteration,Method,Sprintdata,Variable,USES,llm_enhanced
Populateiteration,Method,Modeliterator,Variable,USES,llm_enhanced
Getsprintinfo,Method,Separationstring1,Variable,USES,llm_enhanced
Jiraapplication,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Almclientmetrics,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Configurationrepo,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Configurationcolection,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Metric,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Metric1,Variable,HAS_FIELD,llm_enhanced
Jiraapplication,Class,Jiramain,Method,DECLARES,llm_enhanced
Jiramain,Method,Ctx,Variable,USES,llm_enhanced
Jiramain,Method,Almclientmetrics,Variable,USES,llm_enhanced
Jiramain,Method,Configurationrepo,Variable,USES,llm_enhanced
Jiramain,Method,Configurationcolection,Variable,USES,llm_enhanced
Jiramain,Method,Metric,Variable,USES,llm_enhanced
Jiramain,Method,Metric1,Variable,USES,llm_enhanced
Jiraapplication,Class,Cleanobject,Method,DECLARES,llm_enhanced
Cleanobject,Method,Almclientmetrics,Variable,USES,llm_enhanced
Cleanobject,Method,Configurationrepo,Variable,USES,llm_enhanced
Cleanobject,Method,Configurationcolection,Variable,USES,llm_enhanced
Cleanobject,Method,Metric,Variable,USES,llm_enhanced
Cleanobject,Method,Metric1,Variable,USES,llm_enhanced
Jiraapplication,Class,Deletejiraissues,Method,DECLARES,llm_enhanced
Deletejiraissues,Method,Ctx,Variable,USES,llm_enhanced
Deletejiraissues,Method,Configurationcolection,Variable,USES,llm_enhanced
Deletejiraissues,Method,Almclientmetrics,Variable,USES,llm_enhanced
Deletejiraissues,Method,Configurationrepo,Variable,USES,llm_enhanced
Deletejiraissues,Method,Metric,Variable,USES,llm_enhanced
Deletejiraissues,Method,Metric1,Variable,USES,llm_enhanced
Jiraauthentication,Class,Utf,Variable,HAS_FIELD,llm_enhanced
Jiraauthentication,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Jiraauthentication,Class,Jiraconnectionforstatus,Method,DECLARES,llm_enhanced
Jiraconnectionforstatus,Method,Connectionstatus,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,Jiraurl,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,Loginsearch,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,Encodedbytes,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,Logincreds,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,Basicauth,Variable,USES,llm_enhanced
Jiraconnectionforstatus,Method,In,Variable,USES,llm_enhanced
Jiraauthentication,Class,Jiraconnection,Method,DECLARES,llm_enhanced
Jiraconnection,Method,Connection,Variable,USES,llm_enhanced
Jiraconnection,Method,Datatj,Variable,USES,llm_enhanced
Jiraconnection,Method,Jiraurl,Variable,USES,llm_enhanced
Jiraauthentication,Class,Getboardfilterid,Method,DECLARES,llm_enhanced
Getboardfilterid,Method,Jiraurl,Variable,USES,llm_enhanced
Getboardfilterid,Method,Url,Variable,USES,llm_enhanced
Getboardfilterid,Method,Filterid,Variable,USES,llm_enhanced
Getboardfilterid,Method,Connection,Variable,USES,llm_enhanced
Getboardfilterid,Method,Ur,Variable,USES,llm_enhanced
Getboardfilterid,Method,Login1,Variable,USES,llm_enhanced
Getboardfilterid,Method,Encodedbytes,Variable,USES,llm_enhanced
Getboardfilterid,Method,Logincreds,Variable,USES,llm_enhanced
Getboardfilterid,Method,Basicauth,Variable,USES,llm_enhanced
Getboardfilterid,Method,In,Variable,USES,llm_enhanced
Getboardfilterid,Method,Teststringnew,Variable,USES,llm_enhanced
Getboardfilterid,Method,Parser,Variable,USES,llm_enhanced
Jiraauthentication,Class,Alternatejiraconnection,Method,DECLARES,llm_enhanced
Alternatejiraconnection,Method,Connection,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Jiraurl,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Ur,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Login1,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Encodedbytes,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Logincreds,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Basicauth,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,In,Variable,USES,llm_enhanced
Alternatejiraconnection,Method,Teststringnew,Variable,USES,llm_enhanced
Jiraauthentication,Class,Get,Method,DECLARES,llm_enhanced
Get,Method,Requestfactory,Variable,USES,llm_enhanced
Jiraauthentication,Class,Makerestcall,Method,DECLARES,llm_enhanced
Makerestcall,Method,Builder,Variable,USES,llm_enhanced
Makerestcall,Method,Uricomponents,Variable,USES,llm_enhanced
Makerestcall,Method,Uri,Variable,USES,llm_enhanced
Jiraauthentication,Class,Createheaders,Method,DECLARES,llm_enhanced
Createheaders,Method,Auth,Variable,USES,llm_enhanced
Createheaders,Method,Encodedauth,Variable,USES,llm_enhanced
Createheaders,Method,Authheader,Variable,USES,llm_enhanced
Createheaders,Method,Headers,Variable,USES,llm_enhanced
Jiraclient,Interface,Getalmtooldata,Method,DECLARES,llm_enhanced
Jiraexceptions,Class,Serialversionuid,Variable,HAS_FIELD,llm_enhanced
Jiraexceptions,Class,Jiraexceptions(stringmessage),Method,DECLARES,llm_enhanced
Jiraexceptions,Class,Jiraexceptions(throwablecause),Method,DECLARES,llm_enhanced
Metricsinfo,Class,Getassignee,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Getdescription,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Getfixversion,Method,DECLARES,llm_enhanced
Getfixversion,Method,Fixversionmap,Variable,USES,llm_enhanced
Getfixversion,Method,Reldate,Variable,USES,llm_enhanced
Getfixversion,Method,Time,Variable,USES,llm_enhanced
Getfixversion,Method,Relname,Variable,USES,llm_enhanced
Metricsinfo,Class,Getissuelinks,Method,DECLARES,llm_enhanced
Getissuelinks,Method,Issuelinkjson,Variable,USES,llm_enhanced
Getissuelinks,Method,Outwardlist,Variable,USES,llm_enhanced
Getissuelinks,Method,Jsonlinkfield,Variable,USES,llm_enhanced
Getissuelinks,Method,Outwardissue,Variable,USES,llm_enhanced
Getissuelinks,Method,Inwardissue,Variable,USES,llm_enhanced
Metricsinfo,Class,Getpriority,Method,DECLARES,llm_enhanced
Getpriority,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Getsprintallocation,Method,DECLARES,llm_enhanced
Getsprintallocation,Method,Sprintalocationdate,Variable,USES,llm_enhanced
Getsprintallocation,Method,Historyobject,Variable,USES,llm_enhanced
Getsprintallocation,Method,Itemsarray,Variable,USES,llm_enhanced
Metricsinfo,Class,Getstatus,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Getstorypointallocation,Method,DECLARES,llm_enhanced
Getstorypointallocation,Method,Storypointallocation,Variable,USES,llm_enhanced
Getstorypointallocation,Method,Historyobject,Variable,USES,llm_enhanced
Getstorypointallocation,Method,Itemsarray,Variable,USES,llm_enhanced
Metricsinfo,Class,Getsubtask,Method,DECLARES,llm_enhanced
Getsubtask,Method,Subtasklist,Variable,USES,llm_enhanced
Getsubtask,Method,Subtasksarray,Variable,USES,llm_enhanced
Getsubtask,Method,Subtaskjson,Variable,USES,llm_enhanced
Metricsinfo,Class,Getepicissue,Method,DECLARES,llm_enhanced
Getepicissue,Method,Epicissues,Variable,USES,llm_enhanced
Getepicissue,Method,Itemarray,Variable,USES,llm_enhanced
Metricsinfo,Class,Populateorgestimate,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Setactest,Method,DECLARES,llm_enhanced
Setactest,Method,Timespent,Variable,USES,llm_enhanced
Setactest,Method,Originalestimate,Variable,USES,llm_enhanced
Setactest,Method,Deviation,Variable,USES,llm_enhanced
Metricsinfo,Class,Setaffectedversionsdata,Method,DECLARES,llm_enhanced
Setaffectedversionsdata,Method,Tempkey,Variable,USES,llm_enhanced
Setaffectedversionsdata,Method,Affectedversionslist,Variable,USES,llm_enhanced
Metricsinfo,Class,Setbaseline,Method,DECLARES,llm_enhanced
Setbaseline,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Setcomponentdata,Method,DECLARES,llm_enhanced
Setcomponentdata,Method,Tempkey,Variable,USES,llm_enhanced
Setcomponentdata,Method,Components,Variable,USES,llm_enhanced
Metricsinfo,Class,Setcreationdate,Method,DECLARES,llm_enhanced
Setcreationdate,Method,Createddate,Variable,USES,llm_enhanced
Metricsinfo,Class,Setdefectinjector,Method,DECLARES,llm_enhanced
Setdefectinjector,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Seteffort,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Setissuetype,Method,DECLARES,llm_enhanced
Setissuetype,Method,Statuscategory,Variable,USES,llm_enhanced
Metricsinfo,Class,Setremainingeffort,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Setresolutiondate,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Setseverity,Method,DECLARES,llm_enhanced
Setseverity,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Setstorypoints,Method,DECLARES,llm_enhanced
Setstorypoints,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Setupdateddate,Method,DECLARES,llm_enhanced
Metricsinfo,Class,Splitsprintids,Method,DECLARES,llm_enhanced
Splitsprintids,Method,Allocationmap,Variable,USES,llm_enhanced
Splitsprintids,Method,Allids,Variable,USES,llm_enhanced
Splitsprintids,Method,Sprintid,Variable,USES,llm_enhanced
Metricsinfo,Class,Settargetreleasereports,Method,DECLARES,llm_enhanced
Settargetreleasereports,Method,Targets,Variable,USES,llm_enhanced
Metricsinfo,Class,Setdefectsquads,Method,DECLARES,llm_enhanced
Setdefectsquads,Method,Defectsquads,Variable,USES,llm_enhanced
Metricsinfo,Class,Setdefectcategory,Method,DECLARES,llm_enhanced
Setdefectcategory,Method,Field,Variable,USES,llm_enhanced
Metricsinfo,Class,Setwhenfound,Method,DECLARES,llm_enhanced
Setwhenfound,Method,Field,Variable,USES,llm_enhanced
Metricsinfo,Class,Sethowfound,Method,DECLARES,llm_enhanced
Sethowfound,Method,Field,Variable,USES,llm_enhanced
Metricsinfo,Class,Setwherefound,Method,DECLARES,llm_enhanced
Setwherefound,Method,Field,Variable,USES,llm_enhanced
Metricsinfo,Class,Setenvironment,Method,DECLARES,llm_enhanced
Setenvironment,Method,Tempkey,Variable,USES,llm_enhanced
Metricsinfo,Class,Getlpmcustomfileds,Method,DECLARES,llm_enhanced
Getlpmcustomfileds,Method,Customfields,Variable,USES,llm_enhanced
Getlpmcustomfileds,Method,Name,Variable,USES,llm_enhanced
Getlpmcustomfileds,Method,Key,Variable,USES,llm_enhanced
Getlpmcustomfileds,Method,Field,Variable,USES,llm_enhanced
Rallyauthentication,Class,Utf,Variable,HAS_FIELD,llm_enhanced
Rallyauthentication,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Rallyauthentication,Class,Createheaders,Method,DECLARES,llm_enhanced
Rallyauthentication,Class,Createapiheaders,Method,DECLARES,llm_enhanced
Createapiheaders,Method,Headers,Variable,USES,llm_enhanced
Rallyauthentication,Class,Makerestcallapi,Method,DECLARES,llm_enhanced
Rallyauthentication,Class,Get,Method,DECLARES,llm_enhanced
Rallyauthentication,Class,Callrallyurl,Method,DECLARES,llm_enhanced
Callrallyurl,Method,Rallyjson,Variable,USES,llm_enhanced
Callrallyurl,Method,Arrresult,Variable,USES,llm_enhanced
Callrallyurl,Method,Response,Variable,USES,llm_enhanced
Callrallyurl,Method,Feature,Variable,USES,llm_enhanced
Releaseinfo,Class,Releasecount,Variable,HAS_FIELD,llm_enhanced
Releaseinfo,Class,Donecount,Variable,HAS_FIELD,llm_enhanced
Releaseinfo,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Releaseinfo,Class,Almconfiguration,Variable,HAS_FIELD,llm_enhanced
Releaseinfo,Class,Repo,Variable,HAS_FIELD,llm_enhanced
Releaseinfo,Class,Getreleasedetails,Method,DECLARES,llm_enhanced
Releaseinfo,Class,Cleanobject,Method,DECLARES,llm_enhanced
Releaseinfo,Class,Gettime,Method,DECLARES,llm_enhanced
Releaseinfo,Class,Getreleasestorycount,Method,DECLARES,llm_enhanced
Getreleasedetails,Method,Repo,Variable,USES,llm_enhanced
Getreleasedetails,Method,Almconfigrepo,Variable,USES,llm_enhanced
Getreleasedetails,Method,Almconfiguration,Variable,USES,llm_enhanced
Cleanobject,Method,Almconfigrepo,Variable,USES,llm_enhanced
Cleanobject,Method,Almconfiguration,Variable,USES,llm_enhanced
Cleanobject,Method,Repo,Variable,USES,llm_enhanced
Gettime,Method,Almconfiguration,Variable,USES,llm_enhanced
Sprintwisecalculation,Class,Getsprintdata,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getteamsize,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getplannedstorypoint,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getscmdetails,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getbuildfailure,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Gettd,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getcrtitr,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Getdefectssev,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Storyeffortcalculation,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Gettotaleffort,Method,DECLARES,llm_enhanced
Sprintwisecalculation,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Iterationrepo,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Metricsrepo,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Agg,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Model,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Assignedto,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Sprintmap,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Startdate,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Sprintname,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Projectname,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Closedstate,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Alm_Story,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Configuration,Variable,HAS_FIELD,llm_enhanced
Sprintwisecalculation,Class,Lastsprintenddate,Variable,HAS_FIELD,llm_enhanced
Transitioninfo,Class,Populatetransition,Method,DECLARES,llm_enhanced
Populatetransition,Method,Filteredstatusarray,Variable,USES,llm_enhanced
Populatetransition,Method,Modifieddatelist,Variable,USES,llm_enhanced
Populatetransition,Method,Tasklaststate,Variable,USES,llm_enhanced
Populatetransition,Method,Taskeffort,Variable,USES,llm_enhanced
Populatetransition,Method,Creationtime,Variable,USES,llm_enhanced
Populatetransition,Method,Taskdetailslist,Variable,USES,llm_enhanced
Populatetransition,Method,Modifieddate,Variable,USES,llm_enhanced
Populatetransition,Method,Statewaittime,Variable,USES,llm_enhanced
Populatetransition,Method,Previousstatewaittime,Variable,USES,llm_enhanced
Populatetransition,Method,Leadtime,Variable,USES,llm_enhanced
Populatetransition,Method,Taskdetails,Variable,USES,llm_enhanced
Populatetransition,Method,Fromstate,Variable,USES,llm_enhanced
Populatetransition,Method,Tostate,Variable,USES,llm_enhanced
Populatetransition,Method,Filteredstatusjsonobject,Variable,USES,llm_enhanced
Transitionmetrices,Class,Filteredstatusarray,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Modifieddatelist,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Laststate,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Effort,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Crtime,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Firststate,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Taskdetailslist,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Pname,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Sname,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Projkey,Variable,HAS_FIELD,llm_enhanced
Transitionmetrices,Class,Getprojkey,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setprojkey,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Getfilteredstatusarray,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setfilteredstatusarray,Method,DECLARES,llm_enhanced
Setfilteredstatusarray,Method,Filteredstatusarray,Variable,USES,llm_enhanced
Transitionmetrices,Class,Getmodifieddatelist,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setmodifieddatelist,Method,DECLARES,llm_enhanced
Setmodifieddatelist,Method,Modifieddatelist,Variable,USES,llm_enhanced
Transitionmetrices,Class,Getwid,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setwid,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Getlaststate,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setlaststate,Method,DECLARES,llm_enhanced
Setlaststate,Method,Laststate,Variable,USES,llm_enhanced
Transitionmetrices,Class,Geteffort,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Seteffort,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Getcrtime,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setcrtime,Method,DECLARES,llm_enhanced
Setcrtime,Method,Crtime,Variable,USES,llm_enhanced
Transitionmetrices,Class,Getfirststate,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setfirststate,Method,DECLARES,llm_enhanced
Setfirststate,Method,Firststate,Variable,USES,llm_enhanced
Transitionmetrices,Class,Gettaskdetailslist,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Settaskdetailslist,Method,DECLARES,llm_enhanced
Settaskdetailslist,Method,Taskdetailslist,Variable,USES,llm_enhanced
Transitionmetrices,Class,Getpname,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setpname,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Getsname,Method,DECLARES,llm_enhanced
Transitionmetrices,Class,Setsname,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Commonfunc,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Projectiterationrepo,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Almconfiguration,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Allbugs,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Alliterations,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Alliterationsandbacklog,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Closestates,Variable,HAS_FIELD,llm_enhanced
Backlogcalculation,Class,Getinititialdetails,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Caluclatestoryageing2,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Pushstateflowobject2,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Caluclatestoryageing,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Pushstateflowobject,Method,DECLARES,llm_enhanced
Backlogcalculation,Class,Calculategroomingtable,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Buildrepo,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Builddata,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Builddatalist,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Lastbuild,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Jobdetails,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Jobdetailsrepo,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Mongo,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Buildcalculations,Class,Getvaluestreamsteps,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Buildsteparray,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Getformatteddate,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Getstate,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Getvaluestreampipelinejobs,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Calculatechildsteps,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Getmetric,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Preparepipeline,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Preparejoblist,Method,DECLARES,llm_enhanced
Buildcalculations,Class,Getgitlabvaluestreamsteps,Method,DECLARES,llm_enhanced
Getvaluestreamsteps,Method,Response,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Builddata,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Builddatalist,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Groupedbysteps,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Lastbuild,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Valuestreamstep,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Steps,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Successcount,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Duration,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Executed,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Avgsuccessrate,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Avgduration,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Failed,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Lastfailtime,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Mttrduration,Variable,USES,llm_enhanced
Getvaluestreamsteps,Method,Mttrcount,Variable,USES,llm_enhanced
Buildsteparray,Method,Groupedbysteps,Variable,USES,llm_enhanced
Buildsteparray,Method,Builddata,Variable,USES,llm_enhanced
Buildsteparray,Method,Steps,Variable,USES,llm_enhanced
Getformatteddate,Method,Formatter,Variable,USES,llm_enhanced
Getformatteddate,Method,Calendar,Variable,USES,llm_enhanced
Getstate,Method,Metric,Variable,USES,llm_enhanced
Getstate,Method,State,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Response,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Builddata,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Jobdetails,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Groupbyvaluestream,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Joblist,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Valuestreamstep,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Jobs,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Successcount,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Duration,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Executed,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Avgsuccessrate,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Avgduration,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Failed,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Lastfailtime,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Mttrduration,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Mttrcount,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Status,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Dur,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Durationmetric,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,St,Variable,USES,llm_enhanced
Getvaluestreampipelinejobs,Method,Child,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Child,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Filteredjobs,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Successcount,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Duration,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Executed,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Avgsuccessrate,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Avgduration,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Failed,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Lastfailtime,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Mttrduration,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Mttrcount,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Status,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Dur,Variable,USES,llm_enhanced
Calculatechildsteps,Method,Durationmetric,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Gitlabvaluestreamarr,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Branchname,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Reponameset,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Gitlabbuilddata,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Valuestream,Variable,USES,llm_enhanced
Getgitlabvaluestreamsteps,Method,Valueobj,Variable,USES,llm_enhanced
Commonfunctions,Class,Getcomponentlist,Method,DECLARES,llm_enhanced
Getcomponentlist,Method,Componentlist,Variable,USES,llm_enhanced
Getcomponentlist,Method,Component,Variable,USES,llm_enhanced
Commonfunctions,Class,Converttodisplayvalues,Method,DECLARES,llm_enhanced
Converttodisplayvalues,Method,Seconds,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Minutes,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Hours,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Leftminutes,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Days,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Lefthours,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Week,Variable,USES,llm_enhanced
Converttodisplayvalues,Method,Leftdays,Variable,USES,llm_enhanced
Commonfunctions,Class,Convertmilistodisplayvaluesdefect,Method,DECLARES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Seconds,Variable,USES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Minutes,Variable,USES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Hours,Variable,USES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Leftminutes,Variable,USES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Days,Variable,USES,llm_enhanced
Convertmilistodisplayvaluesdefect,Method,Lefthours,Variable,USES,llm_enhanced
Commonfunctions,Class,Convertsecondstostringdisplay,Method,DECLARES,llm_enhanced
Convertsecondstostringdisplay,Method,Minutes,Variable,USES,llm_enhanced
Convertsecondstostringdisplay,Method,Hours,Variable,USES,llm_enhanced
Convertsecondstostringdisplay,Method,Leftminutes,Variable,USES,llm_enhanced
Convertsecondstostringdisplay,Method,Days,Variable,USES,llm_enhanced
Convertsecondstostringdisplay,Method,Lefthours,Variable,USES,llm_enhanced
Commonfunctions,Class,Tohoursstring,Method,DECLARES,llm_enhanced
Tohoursstring,Method,Minutes,Variable,USES,llm_enhanced
Tohoursstring,Method,Hours,Variable,USES,llm_enhanced
Commonfunctions,Class,Todaysstring,Method,DECLARES,llm_enhanced
Todaysstring,Method,Seconds,Variable,USES,llm_enhanced
Todaysstring,Method,Minutes,Variable,USES,llm_enhanced
Todaysstring,Method,Hours,Variable,USES,llm_enhanced
Todaysstring,Method,Days,Variable,USES,llm_enhanced
Constant,Class,Prodtypeprior,Variable,HAS_FIELD,llm_enhanced
Constant,Class,Prodtypesev,Variable,HAS_FIELD,llm_enhanced
Constant,Class,Prodtypenewdef,Variable,HAS_FIELD,llm_enhanced
Constant,Class,Prodtypewaitdef,Variable,HAS_FIELD,llm_enhanced
Constant,Class,Prodsev,Variable,HAS_FIELD,llm_enhanced
Cryptoutils,Class,Getrandomnonce,Method,DECLARES,llm_enhanced
Getrandomnonce,Method,Nonce,Variable,USES,llm_enhanced
Cryptoutils,Class,Getaeskey,Method,DECLARES,llm_enhanced
Getaeskey,Method,Keygen,Variable,USES,llm_enhanced
Cryptoutils,Class,Getaeskeyfrompassword,Method,DECLARES,llm_enhanced
Getaeskeyfrompassword,Method,Factory,Variable,USES,llm_enhanced
Getaeskeyfrompassword,Method,Spec,Variable,USES,llm_enhanced
Cryptoutils,Class,Hex,Method,DECLARES,llm_enhanced
Cryptoutils,Class,Hexwithblocksize,Method,DECLARES,llm_enhanced
Hexwithblocksize,Method,Hex,Variable,USES,llm_enhanced
Hexwithblocksize,Method,Blocksize,Variable,USES,llm_enhanced
Defectcalculations,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Commonfunc,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Almconfiguration,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Allbugs,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Alliterations,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Alliterationsandbacklog,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Closestates,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Meantimecalcflag,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Totalrem,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Totalspent,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Totmetrics,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Filtermetricsdata,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Filterstroydata,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Currentdate,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Firstsprintstartdate,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Mongo,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Widarr,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Issuelist,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Workingbacklog,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Workingsprints,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Workitemarr2,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Refinedissulist,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Sprintanddefectsadded,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Sprintanddefectsclosed,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Sum,Variable,HAS_FIELD,llm_enhanced
Defectcalculations,Class,Getinititialdetails,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Calculatedefectinsightdatacomponent,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Calculatedefectinsightdata,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Meantimecalculation,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Pushstateflowobject,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Componentsprintdefecttrend,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Sprintdefecttrend,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Defectclassification,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getparetodatacomp,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getparetodata,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getmetricsdata,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Groupdataforpareto,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectproducationslippagecomp,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectproducationslippage,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectdensitycomp,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectdensity,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getmerics,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getstorypoint,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectbacklogcomponent,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Getdefectbacklog,Method,DECLARES,llm_enhanced
Valuecomparator,Class,Base,Variable,HAS_FIELD,llm_enhanced
Valuecomparator,Class,Compare,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Callsp,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Calccloseddefects,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Calcvelocity,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Refinedissues,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Checkremoved,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Checkwithdrawn,Method,DECLARES,llm_enhanced
Defectcalculations,Class,Filtertrans,Method,DECLARES,llm_enhanced
Calculatedefectinsightdata,Method,Closestates,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Averagedefectmapoptional,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Averagedefectwaittimetemp,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Transitions,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Firsttimeflag,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Trans,Variable,USES,llm_enhanced
Calculatedefectinsightdata,Method,Almconfiguration,Variable,USES,llm_enhanced
Valuecomparator,Class,Getmetricsdata,Method,DECLARES,llm_enhanced
Getmetricsdata,Method,Temptime,Variable,USES,llm_enhanced
Getmetricsdata,Method,Metricssource,Variable,USES,llm_enhanced
Groupdataforpareto,Method,Groupeddatabymodule,Variable,USES,llm_enhanced
Getdefectproducationslippagecomp,Method,Componentwisedata,Variable,USES,llm_enhanced
Getdefectproducationslippagecomp,Method,Almconfiguration,Variable,USES,llm_enhanced
Getdefectproducationslippagecomp,Method,Authorproddata,Variable,USES,llm_enhanced
Getdefectproducationslippagecomp,Method,Totalbugstable,Variable,USES,llm_enhanced
Encryptiondecryptionaes,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Encryptiondecryptionaes,Class,Utf_8,Variable,HAS_FIELD,llm_enhanced
Encryptiondecryptionaes,Class,Encrypt,Method,DECLARES,llm_enhanced
Encrypt,Method,Ptext,Variable,USES,llm_enhanced
Encryptiondecryptionaes,Class,Decrypt,Method,DECLARES,llm_enhanced
Encryptoraesgcmpassword,Class,Encrypt_Algo,Variable,HAS_FIELD,llm_enhanced
Encryptoraesgcmpassword,Class,Tag_Length_Bit,Variable,HAS_FIELD,llm_enhanced
Encryptoraesgcmpassword,Class,Iv_Length_Byte,Variable,HAS_FIELD,llm_enhanced
Encryptoraesgcmpassword,Class,Salt_Length_Byte,Variable,HAS_FIELD,llm_enhanced
Encryptoraesgcmpassword,Class,Utf_8,Variable,HAS_FIELD,llm_enhanced
Encryptoraesgcmpassword,Class,Encrypt,Method,DECLARES,llm_enhanced
Encrypt,Method,Salt,Variable,USES,llm_enhanced
Encrypt,Method,Iv,Variable,USES,llm_enhanced
Encrypt,Method,Aeskeyfrompassword,Variable,USES,llm_enhanced
Encrypt,Method,Cipher,Variable,USES,llm_enhanced
Encrypt,Method,Ciphertext,Variable,USES,llm_enhanced
Encrypt,Method,Ciphertextwithivsalt,Variable,USES,llm_enhanced
Encryptoraesgcmpassword,Class,Decrypt,Method,DECLARES,llm_enhanced
Decrypt,Method,Decode,Variable,USES,llm_enhanced
Decrypt,Method,Bb,Variable,USES,llm_enhanced
Decrypt,Method,Iv,Variable,USES,llm_enhanced
Decrypt,Method,Salt,Variable,USES,llm_enhanced
Decrypt,Method,Ciphertext,Variable,USES,llm_enhanced
Decrypt,Method,Aeskeyfrompassword,Variable,USES,llm_enhanced
Decrypt,Method,Cipher,Variable,USES,llm_enhanced
Decrypt,Method,Plaintext,Variable,USES,llm_enhanced
Restclient,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Restclient,Class,Get,Method,DECLARES,llm_enhanced
Restclient,Class,Makegetrestcall,Method,DECLARES,llm_enhanced
Makegetrestcall,Method,Builder,Variable,USES,llm_enhanced
Makegetrestcall,Method,Uricomponents,Variable,USES,llm_enhanced
Makegetrestcall,Method,Uri,Variable,USES,llm_enhanced
Restclient,Class,Createbasicauthheaders,Method,DECLARES,llm_enhanced
Createbasicauthheaders,Method,Auth,Variable,USES,llm_enhanced
Createbasicauthheaders,Method,Encodedauth,Variable,USES,llm_enhanced
Createbasicauthheaders,Method,Authheader,Variable,USES,llm_enhanced
Createbasicauthheaders,Method,Headers,Variable,USES,llm_enhanced
Restclient,Class,Cleanup,Method,DECLARES,llm_enhanced
Cleanup,Method,Coverage,Variable,USES,llm_enhanced
Cleanup,Method,Zip,Variable,USES,llm_enhanced
Restclient,Class,Downloadxml,Method,DECLARES,llm_enhanced
Downloadxml,Method,Authstring,Variable,USES,llm_enhanced
Downloadxml,Method,Authencbytes,Variable,USES,llm_enhanced
Downloadxml,Method,Authstringenc,Variable,USES,llm_enhanced
Downloadxml,Method,Url,Variable,USES,llm_enhanced
Downloadxml,Method,Urlconnection,Variable,USES,llm_enhanced
Downloadxml,Method,Authheader,Variable,USES,llm_enhanced
Downloadxml,Method,Is,Variable,USES,llm_enhanced
Downloadxml,Method,Source,Variable,USES,llm_enhanced
Downloadxml,Method,Target,Variable,USES,llm_enhanced
Restclient,Class,Deletedirectory,Method,DECLARES,llm_enhanced
Restclient,Class,Unzipit,Method,DECLARES,llm_enhanced
Unzipit,Method,Buffer,Variable,USES,llm_enhanced
Unzipit,Method,Folder,Variable,USES,llm_enhanced
Unzipit,Method,Ze,Variable,USES,llm_enhanced
Unzipit,Method,Filename,Variable,USES,llm_enhanced
Unzipit,Method,Newfile,Variable,USES,llm_enhanced
Unzipit,Method,Len,Variable,USES,llm_enhanced
Sprintprogress,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Sprintprogress,Class,Authordata,Variable,HAS_FIELD,llm_enhanced
Sprintprogress,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Sprintprogress,Class,Commonfunc,Variable,HAS_FIELD,llm_enhanced
Sprintprogress,Class,Gettaskrisk,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getissueriskeffortbased,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Setrequiredvalues,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getissueriskstorypoint,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getlateststorypoints,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getissueriskstatus,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getinitialdata,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getburndown,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getburndowneffort,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Getburndownstorypoint,Method,DECLARES,llm_enhanced
Logdatecomparator,Class,Compare,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Isallocated,Method,DECLARES,llm_enhanced
Sprintprogress,Class,Isremoved,Method,DECLARES,llm_enhanced
Sprintprogresscalculations,Class,Ctx,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Metricrepo,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Mongo,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Alliterations,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Allissues,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Authordata,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Storydata,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Metricstaskdata,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Iterationdata,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Almconfigrepo,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Logger,Variable,HAS_FIELD,llm_enhanced
Sprintprogresscalculations,Class,Getinititialdetails,Method,DECLARES,llm_enhanced
Getinititialdetails,Method,Metricrepo,Variable,USES,llm_enhanced
Getinititialdetails,Method,Allissues,Variable,USES,llm_enhanced
Getinititialdetails,Method,Alliterations,Variable,USES,llm_enhanced
Sprintprogresscalculations,Class,Getissuebreakup,Method,DECLARES,llm_enhanced
Getissuebreakup,Method,Response,Variable,USES,llm_enhanced
Getissuebreakup,Method,Spdata,Variable,USES,llm_enhanced
Getissuebreakup,Method,Componentlist,Variable,USES,llm_enhanced
Getissuebreakup,Method,Metrics,Variable,USES,llm_enhanced
Getissuebreakup,Method,Issuebreakuparr,Variable,USES,llm_enhanced
Getissuebreakup,Method,Groupedbytype,Variable,USES,llm_enhanced
Getissuebreakup,Method,Issuesbytype,Variable,USES,llm_enhanced
Getissuebreakup,Method,Type,Variable,USES,llm_enhanced
Getissuebreakup,Method,Sprints,Variable,USES,llm_enhanced
Getissuebreakup,Method,States,Variable,USES,llm_enhanced
Getissuebreakup,Method,Stateslist,Variable,USES,llm_enhanced
Getissuebreakup,Method,Sprintinfo,Variable,USES,llm_enhanced
Getissuebreakup,Method,Sprintname,Variable,USES,llm_enhanced
Getissuebreakup,Method,Issuebysprintandtype,Variable,USES,llm_enhanced
Getissuebreakup,Method,Countmap,Variable,USES,llm_enhanced
Getissuebreakup,Method,Datalist,Variable,USES,llm_enhanced
Sprintprogresscalculations,Class,Getstoryprogress,Method,DECLARES,llm_enhanced
Getstoryprogress,Method,Filterstroydata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Filtermetricsdata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Response,Variable,USES,llm_enhanced
Getstoryprogress,Method,Sprintlist,Variable,USES,llm_enhanced
Getstoryprogress,Method,Mongo,Variable,USES,llm_enhanced
Getstoryprogress,Method,Almconfig,Variable,USES,llm_enhanced
Getstoryprogress,Method,Storydata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Metricstaskdata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Iterationdata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Componentlist,Variable,USES,llm_enhanced
Getstoryprogress,Method,Storyprogressarr,Variable,USES,llm_enhanced
Getstoryprogress,Method,Storytasks,Variable,USES,llm_enhanced
Getstoryprogress,Method,Storypoints,Variable,USES,llm_enhanced
Getstoryprogress,Method,Successpercentage,Variable,USES,llm_enhanced
Getstoryprogress,Method,Spsize,Variable,USES,llm_enhanced
Getstoryprogress,Method,Spdata,Variable,USES,llm_enhanced
Getstoryprogress,Method,Sorted,Variable,USES,llm_enhanced
Getstoryprogress,Method,Successcount,Variable,USES,llm_enhanced
Getstoryprogress,Method,Subtasklength,Variable,USES,llm_enhanced
Getstoryprogress,Method,Sp,Variable,USES,llm_enhanced
Getstoryprogress,Method,Tasklength,Variable,USES,llm_enhanced
Sprintprogresscalculations,Class,Calloperation,Method,DECLARES,llm_enhanced
Storyprogressmodel,Class,Wid,Variable,HAS_FIELD,llm_enhanced
Storyprogressmodel,Class,Storypoint,Variable,HAS_FIELD,llm_enhanced
Storyprogressmodel,Class,Successpercentage,Variable,HAS_FIELD,llm_enhanced
Storyprogressmodel,Class,Getwid,Method,DECLARES,llm_enhanced
Storyprogressmodel,Class,Setwid,Method,DECLARES,llm_enhanced
Storyprogressmodel,Class,Getstorypoint,Method,DECLARES,llm_enhanced
Storyprogressmodel,Class,Setstorypoint,Method,DECLARES,llm_enhanced
Setstorypoint,Method,Storypoint,Variable,USES,llm_enhanced
Storyprogressmodel,Class,Getsuccesspercentage,Method,DECLARES,llm_enhanced
Storyprogressmodel,Class,Setsuccesspercentage,Method,DECLARES,llm_enhanced
Setsuccesspercentage,Method,Successpercentage,Variable,USES,llm_enhanced
Storyprogresssprintwise,Class,Storytasks,Variable,HAS_FIELD,llm_enhanced
Storyprogresssprintwise,Class,Sprintname,Variable,HAS_FIELD,llm_enhanced
Storyprogresssprintwise,Class,Getsprintname,Method,DECLARES,llm_enhanced
Storyprogresssprintwise,Class,Setsprintname,Method,DECLARES,llm_enhanced
Storyprogresssprintwise,Class,Getstorytasks,Method,DECLARES,llm_enhanced
Storyprogresssprintwise,Class,Setstorytasks,Method,DECLARES,llm_enhanced
Setstorytasks,Method,Storytasks,Variable,USES,llm_enhanced
Taskrisksprint,Class,Sprintname,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Startdate,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Enddate,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Estimation,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Completed,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Assignewisedata,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Assigneewisetasks,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Issuecompletionpercentage,Variable,HAS_FIELD,llm_enhanced
Taskrisksprint,Class,Getassigneewisetasks,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setassigneewisetasks,Method,DECLARES,llm_enhanced
Setassigneewisetasks,Method,Assigneewisetasks,Variable,USES,llm_enhanced
Taskrisksprint,Class,Getassignewisedata,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setassignewisedata,Method,DECLARES,llm_enhanced
Setassignewisedata,Method,Assignewisedata,Variable,USES,llm_enhanced
Taskrisksprint,Class,Getsprintname,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setsprintname,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Getstartdate,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setstartdate,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Getenddate,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setenddate,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Getissuecompletionpercentage,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setissuecompletionpercentage,Method,DECLARES,llm_enhanced
Setissuecompletionpercentage,Method,Issuecompletionpercentage,Variable,USES,llm_enhanced
Taskrisksprint,Class,Getestimation,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setestimation,Method,DECLARES,llm_enhanced
Setestimation,Method,Estimation,Variable,USES,llm_enhanced
Taskrisksprint,Class,Getcompleted,Method,DECLARES,llm_enhanced
Taskrisksprint,Class,Setcompleted,Method,DECLARES,llm_enhanced
Setcompleted,Method,Completed,Variable,USES,llm_enhanced
Teamqualityutils,Class,Calclulateabsdiff,Method,DECLARES,llm_enhanced
Teamqualityutils,Class,Calclulatediff,Method,DECLARES,llm_enhanced
Teamqualityutils,Class,Calculatepoints,Method,DECLARES,llm_enhanced
Teamqualityutils,Class,Calculateabspoints,Method,DECLARES,llm_enhanced
Calculatepoints,Method,Diff,Variable,USES,llm_enhanced
Calculateabspoints,Method,Abspoints,Variable,USES,llm_enhanced
Velocitycalculations,Class,Workingbacklog,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Velocityfields,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Closestates,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Tempsprefined,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Tempspremoved,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Storiescompleted,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Defetcscompleted,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Issuelist,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Workitemarr2,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Almconfig,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Scorecardsprintlist,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Refinedissulist,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Removedissulist,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Endatesprint,Variable,HAS_FIELD,llm_enhanced
Velocitycalculations,Class,Calcvelocity,Method,DECLARES,llm_enhanced
Calcvelocity,Method,Workingbacklog,Variable,USES,llm_enhanced
Calcvelocity,Method,Velocityfields,Variable,USES,llm_enhanced
Calcvelocity,Method,Closestates,Variable,USES,llm_enhanced
Velocitycalculations,Class,Callspspillover,Method,DECLARES,llm_enhanced
Callspspillover,Method,Issuelist,Variable,USES,llm_enhanced
Velocitycalculations,Class,Callsp,Method,DECLARES,llm_enhanced
Velocitycalculations,Class,Calcclosedsp,Method,DECLARES,llm_enhanced
Calcclosedsp,Method,Issuelist,Variable,USES,llm_enhanced
Velocitycalculations,Class,Storyloop,Method,DECLARES,llm_enhanced
Velocitycalculations,Class,Filtertrans,Method,DECLARES,llm_enhanced
Velocitycalculations,Class,Storylooprefined,Method,DECLARES,llm_enhanced
Velocitycalculations,Class,Checkremoved,Method,DECLARES,llm_enhanced
Velocitycalculations,Class,Checkwithdrawn,Method,DECLARES,llm_enhanced
Application,Class,Main,Method,EXPOSES,variable_transformations
Triggercollector,Class,Getcontext,Method,CALLS,variable_transformations
Getdatafromtools,Method,Getcontext,Method,CALLS,variable_transformations
Getdatafromtools,Method,Findall,Method,CALLS,variable_transformations
Ctx,Variable,Findall,Method,FLOWS_TO,variable_transformations
Findall,Method,Portfolioconfig,Table,READS_FROM,variable_transformations
Getprojectname,Variable,Jobkey,Variable,FLOWS_TO,variable_transformations
Jobkey,Variable,Job,Variable,FLOWS_TO,variable_transformations
Getschedulerenabled,Variable,Schedulerenabledtrue,Rule,VALIDATES_AGAINST,variable_transformations
Getprojectname,Variable,Notnull,Rule,VALIDATES_AGAINST,variable_transformations
Getcronexpression,Variable,Notnull,Rule,VALIDATES_AGAINST,variable_transformations
Job,Variable,Checkexists,Method,CALLS,variable_transformations
Checkexists,Method,Jobexists,Rule,VALIDATES_AGAINST,variable_transformations
Checkexists,Method,Deletejob,Method,CALLS,variable_transformations
Deletejob,Method,Logger,Variable,LOGS_TO,variable_transformations
Deletejob,Method,Logger,Variable,USES,variable_transformations
Newjob,Method,Joba,Variable,PRODUCES,variable_transformations
Newjob,Method,Joba,Variable,USES,variable_transformations
Getjobdatamap,Method,Jobdatamap,Variable,PRODUCES,variable_transformations
Getjobdatamap,Method,Jobdatamap,Variable,USES,variable_transformations
Jobdatamap,Variable,Getprojectname,Variable,WRITES_TO,variable_transformations
Newtrigger,Method,Trigger,Variable,PRODUCES,variable_transformations
Newtrigger,Method,Trigger,Variable,USES,variable_transformations
Trigger,Variable,Getcronexpression,Variable,WRITES_TO,variable_transformations
Schedulejob,Method,Joba,Variable,CALLS,variable_transformations
Schedulejob,Method,Joba,Variable,USES,variable_transformations
Schedulejob,Method,Trigger,Variable,CALLS,variable_transformations
Schedulejob,Method,Trigger,Variable,USES,variable_transformations
Start,Method,Schedulerinitialization,Operation,CALLS,variable_transformations
Triggercollector,Class,Projectcollector,Class,CALLS,variable_transformations
Req,Variable,Detailsaddsetting,Variable,TRANSFORMS_TO,variable_transformations
Req,Variable,Savealmconfig,Method,FLOWS_TO,variable_transformations
Projectname,Variable,Retrievealmconfig,Method,FLOWS_TO,variable_transformations
Savealmconfig,Method,Almconfiguration,Table,WRITES_TO,variable_transformations
Retrievealmconfig,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Almconfigcontroller,Class,Post:/Almconfig,Endpoint,EXPOSES,variable_transformations
Almconfigcontroller,Class,Get:/Almconfigdetails,Endpoint,EXPOSES,variable_transformations
Almconfigcontroller,Class,Get:/Almconfigdetailsconfig,Endpoint,EXPOSES,variable_transformations
Post:/Almconfig,Endpoint,Req,Variable,ACCEPTS,variable_transformations
Post:/Almconfig,Endpoint,Almconfiguration,Variable,RETURNS,variable_transformations
Get:/Almconfigdetails,Endpoint,Projectname,Variable,ACCEPTS,variable_transformations
Get:/Almconfigdetails,Endpoint,Dataresponse,Variable,RETURNS,variable_transformations
Get:/Almconfigdetailsconfig,Endpoint,Projectname,Variable,ACCEPTS,variable_transformations
Get:/Almconfigdetailsconfig,Endpoint,Dataresponse,Variable,RETURNS,variable_transformations
Retrievelist,Method,Retrievealmconfig,Method,CALLS,variable_transformations
Projname,Variable,Almtype,Variable,FLOWS_TO,variable_transformations
Wid,Variable,Geteffortdata,Method,FLOWS_TO,variable_transformations
Projectname,Variable,Getrelease,Method,FLOWS_TO,variable_transformations
Projname,Variable,Getstoryageingdata,Method,FLOWS_TO,variable_transformations
Almtype,Variable,Getstoryageingdata,Method,FLOWS_TO,variable_transformations
Config,Variable,Almtype,Variable,TRANSFORMS_TO,variable_transformations
Getalmtype,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Getalmtype,Method,Configurationtoolinfometric,Table,READS_FROM,variable_transformations
Updatecomponent,Method,Component,Table,WRITES_TO,variable_transformations
Saveengscore,Method,Engscore,Table,WRITES_TO,variable_transformations
Getiterationdata,Method,Iteration,Table,READS_FROM,variable_transformations
Getdefectcounts,Method,Project,Table,READS_FROM,variable_transformations
Almcontroller,Class,Get:/Storyageing,Endpoint,EXPOSES,variable_transformations
Almcontroller,Class,Get:/Groomingtable,Endpoint,EXPOSES,variable_transformations
Storyageing,Method,Getstoryageingdata,Method,CALLS,variable_transformations
Saveengscore,Method,Engscorevalidator,Validator,VALIDATES_WITH,variable_transformations
Getmetric,Method,Metricaccess,Permission,AUTHORIZES,variable_transformations
Getcomponontwisesprintwisestories,Method,List<Componentsprintwisestories>,Variable,RETURNS,variable_transformations
Getcomponontwisesprintwisestories,Method,List<Componentsprintwisestories>,Variable,USES,variable_transformations
Storyname,Variable,Getstoryname,Method,FLOWS_TO,variable_transformations
Priorityname,Variable,Getpriorityname,Method,FLOWS_TO,variable_transformations
Priorityname,Variable,Setpriorityname,Method,FLOWS_TO,variable_transformations
Projectname,Variable,Getprojectname,Method,FLOWS_TO,variable_transformations
Projectname,Variable,Setprojectname,Method,FLOWS_TO,variable_transformations
Releasename,Variable,Getreleasename,Method,FLOWS_TO,variable_transformations
Releasename,Variable,Setreleasename,Method,FLOWS_TO,variable_transformations
Taskname,Variable,Gettaskname,Method,FLOWS_TO,variable_transformations
Taskname,Variable,Settaskname,Method,FLOWS_TO,variable_transformations
Closestate,Variable,Getclosestate,Method,FLOWS_TO,variable_transformations
Closestate,Variable,Setclosestate,Method,FLOWS_TO,variable_transformations
Criticalpriority,Variable,Getcriticalpriority,Method,FLOWS_TO,variable_transformations
Criticalpriority,Variable,Setcriticalpriority,Method,FLOWS_TO,variable_transformations
Highpriority,Variable,Gethighpriority,Method,FLOWS_TO,variable_transformations
Highpriority,Variable,Sethighpriority,Method,FLOWS_TO,variable_transformations
Medpriority,Variable,Getmedpriority,Method,FLOWS_TO,variable_transformations
Medpriority,Variable,Setmedpriority,Method,FLOWS_TO,variable_transformations
Lowpriority,Variable,Getlowpriority,Method,FLOWS_TO,variable_transformations
Lowpriority,Variable,Setlowpriority,Method,FLOWS_TO,variable_transformations
Defectname,Variable,Getdefectname,Method,FLOWS_TO,variable_transformations
Defectname,Variable,Setdefectname,Method,FLOWS_TO,variable_transformations
Trendtype,Variable,Gettrendtype,Method,FLOWS_TO,variable_transformations
Trendtype,Variable,Settrendtype,Method,FLOWS_TO,variable_transformations
Tracksset,Variable,Gettracksset,Method,FLOWS_TO,variable_transformations
Tracksset,Variable,Settracksset,Method,FLOWS_TO,variable_transformations
Newstate,Variable,Getnewstate,Method,FLOWS_TO,variable_transformations
Newstate,Variable,Setnewstate,Method,FLOWS_TO,variable_transformations
Progressstate,Variable,Getprogressstate,Method,FLOWS_TO,variable_transformations
Progressstate,Variable,Setprogressstate,Method,FLOWS_TO,variable_transformations
Todetailsaddsetting,Method,Details,Variable,PRODUCES,variable_transformations
Todetailsaddsetting,Method,Details,Variable,USES,variable_transformations
Rejectionphase,Variable,Getrejectionphase,Method,FLOWS_TO,variable_transformations
Rejectionphase,Variable,Setrejectionphase,Method,FLOWS_TO,variable_transformations
Reopenphase,Variable,Getreopenphase,Method,FLOWS_TO,variable_transformations
Reopenphase,Variable,Setreopenphase,Method,FLOWS_TO,variable_transformations
Testingphase,Variable,Gettestingphase,Method,FLOWS_TO,variable_transformations
Testingphase,Variable,Settestingphase,Method,FLOWS_TO,variable_transformations
Productionphase,Variable,Getproductionphase,Method,FLOWS_TO,variable_transformations
Productionphase,Variable,Setproductionphase,Method,FLOWS_TO,variable_transformations
Personhours,Variable,Getpersonhours,Method,FLOWS_TO,variable_transformations
Personhours,Variable,Setpersonhours,Method,FLOWS_TO,variable_transformations
Timezone,Variable,Gettimezone,Method,FLOWS_TO,variable_transformations
Timezone,Variable,Settimezone,Method,FLOWS_TO,variable_transformations
Velocityfields,Variable,Getvelocityfields,Method,FLOWS_TO,variable_transformations
Velocityfields,Variable,Setvelocityfields,Method,FLOWS_TO,variable_transformations
Environment,Variable,Getenvironment,Method,FLOWS_TO,variable_transformations
Environment,Variable,Setenvironment,Method,FLOWS_TO,variable_transformations
Safeenabled,Variable,Issafeenabled,Method,FLOWS_TO,variable_transformations
Safeenabled,Variable,Setsafeenabled,Method,FLOWS_TO,variable_transformations
Firstsprint,Variable,Getfirstsprint,Method,FLOWS_TO,variable_transformations
Firstsprint,Variable,Setfirstsprint,Method,FLOWS_TO,variable_transformations
Ccrlabel,Variable,Getccrlabel,Method,FLOWS_TO,variable_transformations
Ccrlabel,Variable,Setccrlabel,Method,FLOWS_TO,variable_transformations
Cycletimestates,Variable,Getcycletimestates,Method,FLOWS_TO,variable_transformations
Cycletimestates,Variable,Setcycletimestates,Method,FLOWS_TO,variable_transformations
Throughputstates,Variable,Getthroughputstates,Method,FLOWS_TO,variable_transformations
Throughputstates,Variable,Setthroughputstates,Method,FLOWS_TO,variable_transformations
Todetailsaddsetting,Method,Almconfiguration,Table,PERSISTS_TO,variable_transformations
Req,Variable,Details,Variable,TRANSFORMS_TO,variable_transformations
Almconfigreq,Class,Getstoryname,Method,EXPOSES,variable_transformations
Almconfigreq,Class,Setstoryname,Method,EXPOSES,variable_transformations
Almconfigreq,Class,Gettaskname,Method,EXPOSES,variable_transformations
Almconfigreq,Class,Settaskname,Method,EXPOSES,variable_transformations
Almconfigreq,Class,Todetailsaddsetting,Method,EXPOSES,variable_transformations
Todetailsaddsetting,Method,Copyproperties,Method,CALLS,variable_transformations
Getalmtype,Method,Almtype,Variable,PRODUCES,variable_transformations
Setalmtype,Method,Almtype,Variable,ACCEPTS,variable_transformations
Username,Variable,Feature,Variable,FLOWS_TO,variable_transformations
Password,Variable,Feature,Variable,FLOWS_TO,variable_transformations
Auth,Class,Get:/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,EXPOSES,variable_transformations
Get:/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Username,Variable,ACCEPTS,variable_transformations
Get:/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Password,Variable,ACCEPTS,variable_transformations
Get:/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Response,Variable,RETURNS,variable_transformations
Authenticationofservice,Method,Authenticate,Method,CALLS,variable_transformations
Authenticate,Method,Clientbuilder.Newclient,Externalservice,INVOKES,variable_transformations
Authenticate,Method,Target,Externalservice,INVOKES,variable_transformations
Authenticate,Method,Request,Externalservice,INVOKES,variable_transformations
Authenticate,Method,Get,Method,CALLS,variable_transformations
Authenticate,Method,Username,Variable,AUTHENTICATES,variable_transformations
Authenticate,Method,Username,Variable,USES,variable_transformations
Authenticate,Method,Password,Variable,AUTHENTICATES,variable_transformations
Authenticate,Method,Password,Variable,USES,variable_transformations
Authenticate,Method,Httpauthenticationfeature,Validator,VALIDATES_WITH,variable_transformations
Lastupdated,Variable,Long,Variable,FLOWS_TO,variable_transformations
Getlastupdated,Method,Lastupdated,Variable,PRODUCES,variable_transformations
Getlastupdated,Method,Lastupdated,Variable,USES,variable_transformations
Dataresponse,Class,Lastupdated,Variable,EXPOSES,variable_transformations
Retrospective,Variable,Savealmconfig,Method,FLOWS_TO,variable_transformations
Savealmconfig,Method,Almconfiguration,Variable,PRODUCES,variable_transformations
Savealmconfig,Method,Almconfiguration,Variable,USES,variable_transformations
Retrievealmconfig,Method,Dataresponse<Almconfiguration>,Variable,PRODUCES,variable_transformations
Retrievealmconfig,Method,Dataresponse<Almconfiguration>,Variable,USES,variable_transformations
Almconfigservice,Class,Post:/Api/Alm-Config/Save,Endpoint,EXPOSES,variable_transformations
Almconfigservice,Class,Get:/Api/Alm-Config/Retrieve,Endpoint,EXPOSES,variable_transformations
Post:/Api/Alm-Config/Save,Endpoint,Retrospective,Variable,ACCEPTS,variable_transformations
Get:/Api/Alm-Config/Retrieve,Endpoint,Projectname,Variable,ACCEPTS,variable_transformations
Post:/Api/Alm-Config/Save,Endpoint,Almconfiguration,Variable,RETURNS,variable_transformations
Get:/Api/Alm-Config/Retrieve,Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS,variable_transformations
Savealmconfig,Method,Saveprojectconfiguration,Businessrule,PROCESSES,variable_transformations
Retrievealmconfig,Method,Fetchprojectconfiguration,Businessrule,PROCESSES,variable_transformations
Retrospective,Variable,Almconfigurationvalidator,Validator,VALIDATES_WITH,variable_transformations
Projectname,Variable,Projectnamevalidator,Validator,VALIDATES_WITH,variable_transformations
Savealmconfig,Method,Adminaccess,Permission,AUTHORIZES,variable_transformations
Retrievealmconfig,Method,Readaccess,Permission,AUTHORIZES,variable_transformations
Req,Variable,Findbyprojectname,Method,FLOWS_TO,variable_transformations
Findbyprojectname,Method,Existingconfig,Variable,PRODUCES,variable_transformations
Findbyprojectname,Method,Existingconfig,Variable,USES,variable_transformations
Req,Variable,Save,Method,FLOWS_TO,variable_transformations
Save,Method,Savedalmconfig,Variable,PRODUCES,variable_transformations
Save,Method,Savedalmconfig,Variable,USES,variable_transformations
Projectname,Variable,Findbyprojectname,Method,FLOWS_TO,variable_transformations
Findbyprojectname,Method,Retrievedconfiglist,Variable,PRODUCES,variable_transformations
Findbyprojectname,Method,Retrievedconfiglist,Variable,USES,variable_transformations
Retrievedconfiglist,Variable,Retrievedconfig,Variable,TRANSFORMS_TO,variable_transformations
Savealmconfig,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Almconfigserviceimplementation,Class,Post:/Api/Alm-Config,Endpoint,EXPOSES,variable_transformations
Almconfigserviceimplementation,Class,Get:/Api/Alm-Config,Endpoint,EXPOSES,variable_transformations
Savealmconfig,Method,Req,Variable,ACCEPTS,variable_transformations
Savealmconfig,Method,Req,Variable,USES,variable_transformations
Retrievealmconfig,Method,Projectname,Variable,ACCEPTS,variable_transformations
Retrievealmconfig,Method,Projectname,Variable,USES,variable_transformations
Retrievealmconfig,Method,Dataresponse,Variable,RETURNS,variable_transformations
Retrievealmconfig,Method,Dataresponse,Variable,USES,variable_transformations
Savealmconfig,Method,Savedalmconfig,Variable,RETURNS,variable_transformations
Savealmconfig,Method,Savedalmconfig,Variable,USES,variable_transformations
Savealmconfig,Method,Findbyprojectname,Method,CALLS,variable_transformations
Savealmconfig,Method,Deletebyprojectname,Method,CALLS,variable_transformations
Savealmconfig,Method,Save,Method,CALLS,variable_transformations
Retrievealmconfig,Method,Findbyprojectname,Method,CALLS,variable_transformations
Savealmconfig,Method,Nonnullprojectname,Rule,VALIDATES_AGAINST,variable_transformations
Retrievealmconfig,Method,Nonemptyprojectname,Rule,VALIDATES_AGAINST,variable_transformations
Getmetricdetails,Method,Wid,Variable,ACCEPTS,variable_transformations
Getmetricdetails,Method,Wid,Variable,USES,variable_transformations
Getmetricdetails,Method,Monogoutmetrics,Variable,RETURNS,variable_transformations
Getmetricdetails,Method,Monogoutmetrics,Variable,USES,variable_transformations
Getallmetrics,Method,Pname,Variable,ACCEPTS,variable_transformations
Getallmetrics,Method,Pname,Variable,USES,variable_transformations
Getallmetrics,Method,List<Monogoutmetrics>,Variable,RETURNS,variable_transformations
Getallmetrics,Method,List<Monogoutmetrics>,Variable,USES,variable_transformations
Getchangesitems,Method,Wid,Variable,ACCEPTS,variable_transformations
Getchangesitems,Method,Wid,Variable,USES,variable_transformations
Getchangesitems,Method,List<Changehistorymodel>,Variable,RETURNS,variable_transformations
Getchangesitems,Method,List<Changehistorymodel>,Variable,USES,variable_transformations
Gettransitionsdata,Method,Wid,Variable,ACCEPTS,variable_transformations
Gettransitionsdata,Method,Wid,Variable,USES,variable_transformations
Gettransitionsdata,Method,List<Transitionmodel>,Variable,RETURNS,variable_transformations
Gettransitionsdata,Method,List<Transitionmodel>,Variable,USES,variable_transformations
Getiterationdata,Method,Pname,Variable,ACCEPTS,variable_transformations
Getiterationdata,Method,Pname,Variable,USES,variable_transformations
Getiterationdata,Method,Itrname,Variable,ACCEPTS,variable_transformations
Getiterationdata,Method,Itrname,Variable,USES,variable_transformations
Getiterationdata,Method,Almtype,Variable,ACCEPTS,variable_transformations
Getiterationdata,Method,Almtype,Variable,USES,variable_transformations
Getiterationdata,Method,Iterationoutmodel,Variable,RETURNS,variable_transformations
Getiterationdata,Method,Iterationoutmodel,Variable,USES,variable_transformations
Geteffortdata,Method,Wid,Variable,ACCEPTS,variable_transformations
Geteffortdata,Method,Wid,Variable,USES,variable_transformations
Geteffortdata,Method,List<Efforthistorymodel>,Variable,RETURNS,variable_transformations
Geteffortdata,Method,List<Efforthistorymodel>,Variable,USES,variable_transformations
Getprojectdetails,Method,Pname,Variable,ACCEPTS,variable_transformations
Getprojectdetails,Method,Pname,Variable,USES,variable_transformations
Getprojectdetails,Method,Almtype,Variable,ACCEPTS,variable_transformations
Getprojectdetails,Method,Almtype,Variable,USES,variable_transformations
Getprojectdetails,Method,List<Iterationoutmodel>,Variable,RETURNS,variable_transformations
Getprojectdetails,Method,List<Iterationoutmodel>,Variable,USES,variable_transformations
Getdefectcounts,Method,Pname,Variable,ACCEPTS,variable_transformations
Getdefectcounts,Method,Pname,Variable,USES,variable_transformations
Getdefectcounts,Method,Almtype,Variable,ACCEPTS,variable_transformations
Getdefectcounts,Method,Almtype,Variable,USES,variable_transformations
Getdefectcounts,Method,Projectmodel,Variable,RETURNS,variable_transformations
Getdefectcounts,Method,Projectmodel,Variable,USES,variable_transformations
Getcrtitr,Method,Pname,Variable,ACCEPTS,variable_transformations
Getcrtitr,Method,Pname,Variable,USES,variable_transformations
Getcrtitr,Method,Almtype,Variable,ACCEPTS,variable_transformations
Getcrtitr,Method,Almtype,Variable,USES,variable_transformations
Getcrtitr,Method,List<Iterationoutmodel>,Variable,RETURNS,variable_transformations
Getcrtitr,Method,List<Iterationoutmodel>,Variable,USES,variable_transformations
Wid,Variable,Datalist,Variable,FLOWS_TO,variable_transformations
Projname,Variable,Sprintwise,Variable,FLOWS_TO,variable_transformations
Get(0),Variable,Component,Variable,TRANSFORMS_TO,variable_transformations
Getengscores(),Variable,Engscore,Variable,TRANSFORMS_TO,variable_transformations
Getunreleasedata,Method,Mongodb,Database,QUERIES,variable_transformations
Geteffortdata,Method,Efforthistoryrepo,Table,READS_FROM,variable_transformations
Req,Variable,Configurationsetting,Variable,FLOWS_TO,variable_transformations
Configurationsetting,Variable,Dataresponse,Variable,TRANSFORMS_TO,variable_transformations
Addconfig,Method,Configurationsetting,Table,PERSISTS_TO,variable_transformations
Deleteconfig,Method,Configurationsetting,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Projectdatabase,Database,QUERIES,variable_transformations
Deleteproject,Method,Projectdatabase,Database,QUERIES,variable_transformations
Getconfigproject,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Configurationsettingservice,Class,Get:/Api/Configurations,Endpoint,EXPOSES,variable_transformations
Configurationsettingservice,Class,Post:/Api/Configurations,Endpoint,EXPOSES,variable_transformations
Configurationsettingservice,Class,Delete:/Api/Configurations/{Id},Endpoint,EXPOSES,variable_transformations
Configurationsettingservice,Class,Delete:/Api/Projects/{Projectname},Endpoint,EXPOSES,variable_transformations
Get:/Api/Configurations,Endpoint,Dataresponse,Variable,RETURNS,variable_transformations
Post:/Api/Configurations,Endpoint,Configurationsetting,Variable,ACCEPTS,variable_transformations
Addconfig,Method,Saveconfigsetting,Operation,TRIGGERS,variable_transformations
Deleteconfig,Method,Deletesingleconfig,Businessrule,PROCESSES,variable_transformations
Deleteallcollections,Method,Clearallcollections,Operation,TRIGGERS,variable_transformations
Deleteproject,Method,Removeproject,Businessrule,PROCESSES,variable_transformations
Getconfigproject,Method,Findbyprojectname,Method,CALLS,variable_transformations
Configurationsetting,Variable,Configvalidator,Validator,VALIDATES_WITH,variable_transformations
Deleteproject,Method,Deletepermission,Permission,AUTHORIZES,variable_transformations
Deleteconfig,Method,Deletepermission,Permission,AUTHORIZES,variable_transformations
Gettime(),Variable,Settimestamp(),Variable,FLOWS_TO,variable_transformations
Getprojectname()),Variable,Nullcheck,Rule,VALIDATES_AGAINST,variable_transformations
Req,Variable,Save(Req),Variable,WRITES_TO,variable_transformations
Req,Variable,Getprojectname()),Variable,READS_FROM,variable_transformations
Getconfig,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Addconfig,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Addconfig,Method,Configurationsetting,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Almconfig,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Buildtool,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Buildfailurepatternforproject,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Codecoverage,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Codequality,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Healthdata,Table,WRITES_TO,variable_transformations
Deleteallcollections,Method,Scmtool,Table,WRITES_TO,variable_transformations
Deleteproject,Method,Deleteallcollections,Method,CALLS,variable_transformations
Deleteproject,Method,Portfolioconfig,Table,WRITES_TO,variable_transformations
Deleteproject,Method,Projecthealth,Table,WRITES_TO,variable_transformations
Deleteproject,Method,Chartconfig,Table,WRITES_TO,variable_transformations
Deleteproject,Method,Userassociation,Table,WRITES_TO,variable_transformations
Deleteproject,Method,Goalsetting,Table,WRITES_TO,variable_transformations
Configurationsettingserviceimplementation,Class,Get:/Api/Config,Endpoint,EXPOSES,variable_transformations
Getconfig,Method,Get:/Api/Config,Endpoint,MAPS_TO,variable_transformations
Getconfigproject,Method,Get:/Api/Config/Project/{Projectname},Endpoint,MAPS_TO,variable_transformations
Addconfig,Method,Post:/Api/Config,Endpoint,MAPS_TO,variable_transformations
Deleteconfig,Method,Delete:/Api/Config,Endpoint,MAPS_TO,variable_transformations
Deleteallcollections,Method,Delallissues,Method,CALLS,variable_transformations
Deleteallcollections,Method,Findbyname,Method,CALLS,variable_transformations
Deleteallcollections,Method,Findbyprojectname,Method,CALLS,variable_transformations
Getconfigproject,Method,Secretkey,Method,CALLS,variable_transformations
Getconfigproject,Method,Decrypt,Method,CALLS,variable_transformations
Getprojectname(),Variable,Nullcheck,Validator,VALIDATES_WITH,variable_transformations
Projectname,Variable,Isnullorempty,Validator,VALIDATES_WITH,variable_transformations
Getpassword(),Variable,Decrypt,Validator,VALIDATES_WITH,variable_transformations
Getconfig,Method,Cacheable:Getconfig,Permission,AUTHORIZES,variable_transformations
Getconfigproject,Method,Cacheable:Getconfigproject,Permission,AUTHORIZES,variable_transformations
Format,Variable,Pattern,Variable,FLOWS_TO,variable_transformations
Pattern,Variable,Simpledateformat,Variable,FLOWS_TO,variable_transformations
Getdateinformat,Method,Formatteddate,Variable,PRODUCES,variable_transformations
Getdateinformat,Method,Formatteddate,Variable,USES,variable_transformations
Dateinput,Variable,Formatteddate,Variable,TRANSFORMS_TO,variable_transformations
Getlastweekworkingdaterange,Method,Getdateinformat,Method,CALLS,variable_transformations
Date,Variable,Calendarsetup,Operation,TRIGGERS,variable_transformations
Date,Variable,Daysago,Variable,TRANSFORMS_TO,variable_transformations
Projectname,Variable,Getlastrun,Method,FLOWS_TO,variable_transformations
Type,Variable,Getlastrun,Method,FLOWS_TO,variable_transformations
Timestamp,Variable,Getlastrun,Method,FLOWS_TO,variable_transformations
Mail,Variable,Emailnotification::sendmail,Method,FLOWS_TO,variable_transformations
Timestamp,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Getlastrun,Method,Mailsetup,Table,READS_FROM,variable_transformations
Getlastrun,Method,Collectorlastrun,Table,READS_FROM,variable_transformations
Getlastrun,Method,Collectorlastrun,Table,WRITES_TO,variable_transformations
Constantvariable,Class,Get:/Portfolioviewconfig,Endpoint,EXPOSES,variable_transformations
Constantvariable,Class,Post:/Almconfig,Endpoint,EXPOSES,variable_transformations
Constantvariable,Class,Get:/Retrievemailconfiguration,Endpoint,EXPOSES,variable_transformations
Constantvariable,Class,Post:/Updateuser,Endpoint,EXPOSES,variable_transformations
Constantvariable,Class,Get:/Activesprints,Endpoint,EXPOSES,variable_transformations
Getpreviousdate,Method,Minusdays,Method,CALLS,variable_transformations
Timestamp,Method,Datetimezone::forid,Method,CALLS,variable_transformations
Timestamp,Method,Getcontext,Method,CALLS,variable_transformations
Getlastrun,Method,Getcontext,Method,CALLS,variable_transformations
Lastrunmap,Variable,Updatecollectorlastrun,Operation,TRIGGERS,variable_transformations
Getlastrun,Method,Emailnotification::sendmail,Method,CALLS,variable_transformations
Ctx,Variable,Portfoliorepo,Variable,FLOWS_TO,variable_transformations
Multitaskthread,Method,Configs,Variable,PRODUCES,variable_transformations
Multitaskthread,Method,Configs,Variable,USES,variable_transformations
Configs,Variable,Config,Variable,FLOWS_TO,variable_transformations
Config,Variable,Config.Getschedulerenabled(),Rule,VALIDATES_AGAINST,variable_transformations
Config,Variable,Configurationcolection,Variable,FLOWS_TO,variable_transformations
Configurationcolection,Variable,Listoftools,Variable,FLOWS_TO,variable_transformations
Listoftools,Variable,Gettoolname(),Variable,FLOWS_TO,variable_transformations
Gettoolname(),Variable,Makeswitchcasecall,Method,TRIGGERS,variable_transformations
Projectname,Variable,Msgbody,Variable,FLOWS_TO,variable_transformations
Smtpfieldsarray,Variable,Props,Variable,TRANSFORMS_TO,variable_transformations
Mailsetupmodellist,Variable,Smtpfieldsarray,Variable,TRANSFORMS_TO,variable_transformations
Toaddress,Variable,Messagebodypart,Variable,TRANSFORMS_TO,variable_transformations
Msgbody,Variable,Messagebodypart,Variable,TRANSFORMS_TO,variable_transformations
Multitaskthread,Method,Portfolioconfig,Table,READS_FROM,variable_transformations
Multitaskthread,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Getsmtpinfo,Method,Mailsetup,Table,READS_FROM,variable_transformations
Projectcollector,Class,Post:/Scheduler,Endpoint,EXPOSES,variable_transformations
Execute,Method,Post:/Scheduler,Endpoint,MAPS_TO,variable_transformations
Multitaskthread,Method,Makeswitchcasecall,Method,CALLS,variable_transformations
Multitaskthread,Method,Destroy,Method,CALLS,variable_transformations
Makeswitchcasecall,Method,Bitbucketpipeline,Method,CALLS,variable_transformations
Sendmail,Method,Getsmtpinfo,Method,CALLS,variable_transformations
Sendmail,Method,Transport.Send,Externalservice,CALLS,variable_transformations
Destroy,Method,Projecthealth,Method,CALLS,variable_transformations
Destroy,Method,Sprintcomparison,Method,CALLS,variable_transformations
Destroy,Method,Evictallcaches,Method,CALLS,variable_transformations
Config,Variable,Schedulervalidator,Validator,VALIDATES_WITH,variable_transformations
Sendmail,Method,Smtpfieldsarray[2],User,AUTHENTICATES,variable_transformations
Sendmail,Method,Mailsetup.Isstarttls,Permission,AUTHORIZES,variable_transformations
Host,Variable,Db,Variable,FLOWS_TO,variable_transformations
Db,Variable,Userid,Variable,FLOWS_TO,variable_transformations
Userid,Variable,Password,Variable,FLOWS_TO,variable_transformations
Password,Variable,Port,Variable,FLOWS_TO,variable_transformations
Port,Variable,Secret,Variable,FLOWS_TO,variable_transformations
Host,Variable,Replicaslist,Variable,FLOWS_TO,variable_transformations
Port,Variable,Replicaslist,Variable,FLOWS_TO,variable_transformations
Dbuserid,Variable,Mongocredential,Variable,FLOWS_TO,variable_transformations
Db,Variable,Mongocredential,Variable,FLOWS_TO,variable_transformations
Dbpassword,Variable,Mongocredential,Variable,FLOWS_TO,variable_transformations
Mongocredential,Variable,Client,Variable,FLOWS_TO,variable_transformations
Replicaslist,Variable,Client,Variable,FLOWS_TO,variable_transformations
Client,Variable,Mongotemplate,Variable,FLOWS_TO,variable_transformations
Dbuserid,Variable,Userid,Variable,FLOWS_TO,variable_transformations
Dbpassword,Variable,Password,Variable,FLOWS_TO,variable_transformations
Db,Variable,Getdatabasename,Variable,FLOWS_TO,variable_transformations
Mongotemplate,Variable,Mongo,Variable,TRANSFORMS_TO,variable_transformations
Mongo,Method,Configuration,Table,READS_FROM,variable_transformations
Mongo,Method,Configuration,Table,WRITES_TO,variable_transformations
Mongotemplate,Method,Mongodb,Database,QUERIES,variable_transformations
Mongotemplate,Method,Mongodb,Database,WRITES_TO,variable_transformations
Dataconfig,Class,Methods Via Mongotemplate,Endpoint,EXPOSES,variable_transformations
Getinstance,Method,Dataconfig(constructor),Method,CALLS,variable_transformations
Mongo,Method,Getdatabasename,Method,CALLS,variable_transformations
Mongotemplate,Method,Mongo,Method,CALLS,variable_transformations
Mongotemplate,Method,Getdatabasename,Method,CALLS,variable_transformations
Getcontext,Method,Annotationconfigapplicationcontext,Method,CALLS,variable_transformations
Dbpassword,Variable,Tostring,Rule,VALIDATES_AGAINST,variable_transformations
Db,Variable,Isempty,Rule,VALIDATES_AGAINST,variable_transformations
Effortsgrouped,Variable,Setefforts,Variable,FLOWS_TO,variable_transformations
Transitiongrouped,Variable,Settransitions,Variable,FLOWS_TO,variable_transformations
Getcomponents(),Variable,Componentname,Variable,FLOWS_TO,variable_transformations
Aggregatemetrics,Method,Metrics,Table,READS_FROM,variable_transformations
Aggregatemetrics,Method,Transition,Table,READS_FROM,variable_transformations
Aggregatemetrics,Method,Efforthistory,Table,READS_FROM,variable_transformations
Deleteissues,Method,Metrics,Table,WRITES_TO,variable_transformations
Deleteissues,Method,Transition,Table,WRITES_TO,variable_transformations
Deleteissues,Method,Efforthistory,Table,WRITES_TO,variable_transformations
Deleteissues,Method,Changeitems,Table,WRITES_TO,variable_transformations
Aggregate,Method,Author,Table,WRITES_TO,variable_transformations
Aggregate,Method,Projectiterationrepo,Table,WRITES_TO,variable_transformations
Mongoaggregate,Class,Get:/Metrics,Endpoint,EXPOSES,variable_transformations
Mongoaggregate,Class,Post:/Aggregate,Endpoint,EXPOSES,variable_transformations
Mongoaggregate,Class,Delete:/Issues,Endpoint,EXPOSES,variable_transformations
Mongoaggregate,Class,Get:/Sprints/Count,Endpoint,EXPOSES,variable_transformations
Aggregatemetrics,Method,Getcontext,Method,CALLS,variable_transformations
Aggregate,Method,Getlookup,Method,CALLS,variable_transformations
Aggregate,Method,Getcontext,Method,CALLS,variable_transformations
Getcurrentitr,Method,Match,Method,CALLS,variable_transformations
Updatecomponentfortaskandsubtasks,Method,Save,Method,CALLS,variable_transformations
Updatecomponentfortaskandsubtasks,Method,Aggregate,Method,CALLS,variable_transformations
Id,Variable,Getid,Method,PRODUCES,variable_transformations
Id,Variable,Setid,Method,FLOWS_TO,variable_transformations
Getid,Method,Id,Variable,RETURNS,variable_transformations
Getid,Method,Id,Variable,USES,variable_transformations
Setid,Method,Id,Variable,ACCEPTS,variable_transformations
Projkey,Variable,Pname,Variable,FLOWS_TO,variable_transformations
Wid,Variable,Field,Variable,FLOWS_TO,variable_transformations
Oldvalue,Variable,Newvalue,Variable,FLOWS_TO,variable_transformations
Changehistorymodel,Class,Changeitems,Table,PERSISTS_TO,variable_transformations
Setprojkey,Method,Changeitems,Table,WRITES_TO,variable_transformations
Setprojectname,Method,Changeitems,Table,WRITES_TO,variable_transformations
Setwid,Method,Changeitems,Table,WRITES_TO,variable_transformations
Componentvelocitylist,Class,Component,Variable,DEFINES,variable_transformations
Componentvelocitylist,Class,Velocitylist,Variable,DEFINES,variable_transformations
Getcomponent,Method,Component,Variable,PRODUCES,variable_transformations
Getcomponent,Method,Component,Variable,USES,variable_transformations
Setcomponent,Method,Component,Variable,ACCEPTS,variable_transformations
Getvelocitylist,Method,Velocitylist,Variable,PRODUCES,variable_transformations
Getvelocitylist,Method,Velocitylist,Variable,USES,variable_transformations
Setvelocitylist,Method,Velocitylist,Variable,ACCEPTS,variable_transformations
Setvelocitylist,Method,Velocitylist,Variable,USES,variable_transformations
Setmanualdata,Method,Manualdata,Variable,FLOWS_TO,variable_transformations
Settimestamp,Method,Timestamp,Variable,FLOWS_TO,variable_transformations
Setprojectname,Method,Projectname,Variable,FLOWS_TO,variable_transformations
Setaddflag,Method,Addflag,Variable,FLOWS_TO,variable_transformations
Setbaseline,Method,Baseline,Variable,FLOWS_TO,variable_transformations
Setprojecttype,Method,Projecttype,Variable,FLOWS_TO,variable_transformations
Configurationsetting,Class,Configuration,Table,PERSISTS_TO,variable_transformations
Ismanualdata,Method,Manualdata,Variable,PRODUCES,variable_transformations
Ismanualdata,Method,Manualdata,Variable,USES,variable_transformations
Gettimestamp,Method,Timestamp,Variable,PRODUCES,variable_transformations
Gettimestamp,Method,Timestamp,Variable,USES,variable_transformations
Getprojectname,Method,Projectname,Variable,PRODUCES,variable_transformations
Getprojectname,Method,Projectname,Variable,USES,variable_transformations
Getmetrics,Method,Metric,Variable,PRODUCES,variable_transformations
Getmetrics,Method,Metric,Variable,USES,variable_transformations
Isaddflag,Method,Addflag,Variable,PRODUCES,variable_transformations
Isaddflag,Method,Addflag,Variable,USES,variable_transformations
Isbaseline,Method,Baseline,Variable,PRODUCES,variable_transformations
Isbaseline,Method,Baseline,Variable,USES,variable_transformations
Getprojecttype,Method,Projecttype,Variable,PRODUCES,variable_transformations
Getprojecttype,Method,Projecttype,Variable,USES,variable_transformations
Setmanualdata,Method,Updatemanualdataflag,Operation,TRIGGERS,variable_transformations
Settimestamp,Method,Updatetimestamp,Operation,TRIGGERS,variable_transformations
Setprojectname,Method,Updateprojectname,Operation,TRIGGERS,variable_transformations
Setaddflag,Method,Updateaddflag,Operation,TRIGGERS,variable_transformations
Setbaseline,Method,Updatebaselineflag,Operation,TRIGGERS,variable_transformations
Setprojecttype,Method,Updateprojecttype,Operation,TRIGGERS,variable_transformations
Manualdata,Variable,Booleanvalidator,Validator,VALIDATES_WITH,variable_transformations
Timestamp,Variable,Timestampvalidator,Validator,VALIDATES_WITH,variable_transformations
Projectname,Variable,Stringvalidator,Validator,VALIDATES_WITH,variable_transformations
Addflag,Variable,Booleanvalidator,Validator,VALIDATES_WITH,variable_transformations
Baseline,Variable,Booleanvalidator,Validator,VALIDATES_WITH,variable_transformations
Projecttype,Variable,Stringvalidator,Validator,VALIDATES_WITH,variable_transformations
Getid,Method,Id,Variable,PRODUCES,variable_transformations
Gettoolname,Method,Toolname,Variable,PRODUCES,variable_transformations
Gettoolname,Method,Toolname,Variable,USES,variable_transformations
Geturl,Method,Url,Variable,PRODUCES,variable_transformations
Geturl,Method,Url,Variable,USES,variable_transformations
Getusername,Method,Username,Variable,PRODUCES,variable_transformations
Getusername,Method,Username,Variable,USES,variable_transformations
Getpassword,Method,Password,Variable,PRODUCES,variable_transformations
Getpassword,Method,Password,Variable,USES,variable_transformations
Gettooltype,Method,Tooltype,Variable,PRODUCES,variable_transformations
Gettooltype,Method,Tooltype,Variable,USES,variable_transformations
Getwidgetname,Method,Widgetname,Variable,PRODUCES,variable_transformations
Getwidgetname,Method,Widgetname,Variable,USES,variable_transformations
Getjobname,Method,Jobname,Variable,PRODUCES,variable_transformations
Getjobname,Method,Jobname,Variable,USES,variable_transformations
Getprojectcode,Method,Projectcode,Variable,PRODUCES,variable_transformations
Getprojectcode,Method,Projectcode,Variable,USES,variable_transformations
Getselected,Method,Selected,Variable,PRODUCES,variable_transformations
Getselected,Method,Selected,Variable,USES,variable_transformations
Getdomain,Method,Domain,Variable,PRODUCES,variable_transformations
Getdomain,Method,Domain,Variable,USES,variable_transformations
Gethost,Method,Host,Variable,PRODUCES,variable_transformations
Gethost,Method,Host,Variable,USES,variable_transformations
Getport,Method,Port,Variable,PRODUCES,variable_transformations
Getport,Method,Port,Variable,USES,variable_transformations
Getdbtype,Method,Dbtype,Variable,PRODUCES,variable_transformations
Getdbtype,Method,Dbtype,Variable,USES,variable_transformations
Getschema,Method,Schema,Variable,PRODUCES,variable_transformations
Getschema,Method,Schema,Variable,USES,variable_transformations
Getreponame,Method,Reponame,Variable,PRODUCES,variable_transformations
Getreponame,Method,Reponame,Variable,USES,variable_transformations
Getsecret,Method,Secret,Variable,PRODUCES,variable_transformations
Getsecret,Method,Secret,Variable,USES,variable_transformations
Setid,Method,Idvalidation,Rule,VALIDATES_AGAINST,variable_transformations
Setpassword,Method,Passwordvalidation,Rule,VALIDATES_AGAINST,variable_transformations
Toolname,Variable,Configuretool,Operation,TRIGGERS,variable_transformations
Manualdata,Variable,Switchtomanualmode,Operation,TRIGGERS,variable_transformations
Password,Variable,Passwordvalidator,Validator,VALIDATES_WITH,variable_transformations
Secret,Variable,Secretvalidator,Validator,VALIDATES_WITH,variable_transformations
Customfields,Class,Name,Variable,PRODUCES,variable_transformations
Getname,Method,Name,Variable,RETURNS,variable_transformations
Getname,Method,Name,Variable,USES,variable_transformations
Setname,Method,Name,Variable,ACCEPTS,variable_transformations
Iterationmodel,Class,Iterations,Table,PERSISTS_TO,variable_transformations
Compareto,Method,Compare,Method,CALLS,variable_transformations
Iterationoutmodel,Class,Author,Table,PERSISTS_TO,variable_transformations
Iterationoutmodel,Class,Basemodel,Class,INHERITS_FROM,variable_transformations
Metrics,Variable,Monogoutmetrics,Class,RELATES_TO,variable_transformations
Metricsmodel,Class,Metrics,Table,PERSISTS_TO,variable_transformations
Getcustomfields,Method,Customfields,Variable,CALLS,variable_transformations
Getcustomfields,Method,Customfields,Variable,USES,variable_transformations
Setcustomfields,Method,Customfields,Variable,CALLS,variable_transformations
Getactest,Method,Actest,Variable,CALLS,variable_transformations
Getactest,Method,Actest,Variable,USES,variable_transformations
Setactest,Method,Actest,Variable,CALLS,variable_transformations
Getaffectedversions,Method,Affectedversions,Variable,CALLS,variable_transformations
Getaffectedversions,Method,Affectedversions,Variable,USES,variable_transformations
Setaffectedversions,Method,Affectedversions,Variable,CALLS,variable_transformations
Getallocateddate,Method,Allocateddate,Variable,CALLS,variable_transformations
Getallocateddate,Method,Allocateddate,Variable,USES,variable_transformations
Setallocateddate,Method,Allocateddate,Variable,CALLS,variable_transformations
Getassgnto,Method,Assgnto,Variable,CALLS,variable_transformations
Getassgnto,Method,Assgnto,Variable,USES,variable_transformations
Setassgnto,Method,Assgnto,Variable,CALLS,variable_transformations
Getbaseline,Method,Baseline,Variable,CALLS,variable_transformations
Getbaseline,Method,Baseline,Variable,USES,variable_transformations
Setbaseline,Method,Baseline,Variable,CALLS,variable_transformations
Getcomponents,Method,Components,Variable,CALLS,variable_transformations
Getcomponents,Method,Components,Variable,USES,variable_transformations
Setcomponents,Method,Components,Variable,CALLS,variable_transformations
Getcreatedate,Method,Createdate,Variable,CALLS,variable_transformations
Getcreatedate,Method,Createdate,Variable,USES,variable_transformations
Setcreatedate,Method,Createdate,Variable,CALLS,variable_transformations
Getcycletime,Method,Cycletime,Variable,CALLS,variable_transformations
Getcycletime,Method,Cycletime,Variable,USES,variable_transformations
Setcycletime,Method,Cycletime,Variable,CALLS,variable_transformations
Getdefectinjector,Method,Defectinjector,Variable,CALLS,variable_transformations
Getdefectinjector,Method,Defectinjector,Variable,USES,variable_transformations
Setdefectinjector,Method,Defectinjector,Variable,CALLS,variable_transformations
Getdonedate,Method,Donedate,Variable,CALLS,variable_transformations
Getdonedate,Method,Donedate,Variable,USES,variable_transformations
Setdonedate,Method,Donedate,Variable,CALLS,variable_transformations
Geteffort,Method,Effort,Variable,CALLS,variable_transformations
Geteffort,Method,Effort,Variable,USES,variable_transformations
Seteffort,Method,Effort,Variable,CALLS,variable_transformations
Getefforts,Method,Efforts,Variable,CALLS,variable_transformations
Getefforts,Method,Efforts,Variable,USES,variable_transformations
Setefforts,Method,Efforts,Variable,CALLS,variable_transformations
Setefforts,Method,Efforts,Variable,USES,variable_transformations
Getepiclink,Method,Epiclink,Variable,CALLS,variable_transformations
Getepiclink,Method,Epiclink,Variable,USES,variable_transformations
Setepiclink,Method,Epiclink,Variable,CALLS,variable_transformations
Getestchange,Method,Estchange,Variable,CALLS,variable_transformations
Getestchange,Method,Estchange,Variable,USES,variable_transformations
Setestchange,Method,Estchange,Variable,CALLS,variable_transformations
Getexteffort,Method,Exteffort,Variable,CALLS,variable_transformations
Getexteffort,Method,Exteffort,Variable,USES,variable_transformations
Setexteffort,Method,Exteffort,Variable,CALLS,variable_transformations
Getfixver,Method,Fixver,Variable,CALLS,variable_transformations
Getfixver,Method,Fixver,Variable,USES,variable_transformations
Setfixver,Method,Fixver,Variable,CALLS,variable_transformations
Getinwardissuelink,Method,Inwardissuelink,Variable,CALLS,variable_transformations
Getinwardissuelink,Method,Inwardissuelink,Variable,USES,variable_transformations
Setinwardissuelink,Method,Inwardissuelink,Variable,CALLS,variable_transformations
Getleadtime,Method,Leadtime,Variable,CALLS,variable_transformations
Getleadtime,Method,Leadtime,Variable,USES,variable_transformations
Setleadtime,Method,Leadtime,Variable,CALLS,variable_transformations
Getorgest,Method,Orgest,Variable,CALLS,variable_transformations
Getorgest,Method,Orgest,Variable,USES,variable_transformations
Setorgest,Method,Orgest,Variable,CALLS,variable_transformations
Getoutwardissuelink,Method,Outwardissuelink,Variable,CALLS,variable_transformations
Getoutwardissuelink,Method,Outwardissuelink,Variable,USES,variable_transformations
Setoutwardissuelink,Method,Outwardissuelink,Variable,CALLS,variable_transformations
Getpalmtype,Method,Palmtype,Variable,CALLS,variable_transformations
Getpalmtype,Method,Palmtype,Variable,USES,variable_transformations
Setpalmtype,Method,Palmtype,Variable,CALLS,variable_transformations
Getpname,Method,Pname,Variable,CALLS,variable_transformations
Getpname,Method,Pname,Variable,USES,variable_transformations
Setpname,Method,Pname,Variable,CALLS,variable_transformations
Getpriority,Method,Priority,Variable,CALLS,variable_transformations
Getpriority,Method,Priority,Variable,USES,variable_transformations
Setpriority,Method,Priority,Variable,CALLS,variable_transformations
Getremtime,Method,Remtime,Variable,CALLS,variable_transformations
Getremtime,Method,Remtime,Variable,USES,variable_transformations
Setremtime,Method,Remtime,Variable,CALLS,variable_transformations
Getresdate,Method,Resdate,Variable,CALLS,variable_transformations
Getresdate,Method,Resdate,Variable,USES,variable_transformations
Setresdate,Method,Resdate,Variable,CALLS,variable_transformations
Getseverity,Method,Severity,Variable,CALLS,variable_transformations
Getseverity,Method,Severity,Variable,USES,variable_transformations
Setseverity,Method,Severity,Variable,CALLS,variable_transformations
Getsid,Method,Sid,Variable,CALLS,variable_transformations
Getsid,Method,Sid,Variable,USES,variable_transformations
Setsid,Method,Sid,Variable,CALLS,variable_transformations
Getsname,Method,Sname,Variable,CALLS,variable_transformations
Getsname,Method,Sname,Variable,USES,variable_transformations
Setsname,Method,Sname,Variable,CALLS,variable_transformations
Getstate,Method,State,Variable,CALLS,variable_transformations
Setstate,Method,State,Variable,CALLS,variable_transformations
Getstateset,Method,Stateset,Variable,CALLS,variable_transformations
Getstateset,Method,Stateset,Variable,USES,variable_transformations
Setstateset,Method,Stateset,Variable,CALLS,variable_transformations
Getsubtasklist,Method,Subtasklist,Variable,CALLS,variable_transformations
Getsubtasklist,Method,Subtasklist,Variable,USES,variable_transformations
Setsubtasklist,Method,Subtasklist,Variable,CALLS,variable_transformations
Getlabel,Method,Label,Variable,CALLS,variable_transformations
Getlabel,Method,Label,Variable,USES,variable_transformations
Setlabel,Method,Label,Variable,CALLS,variable_transformations
Setlabel,Method,Label,Variable,USES,variable_transformations
Gettargetrelease,Method,Targetrelease,Variable,CALLS,variable_transformations
Gettargetrelease,Method,Targetrelease,Variable,USES,variable_transformations
Settargetrelease,Method,Targetrelease,Variable,CALLS,variable_transformations
Getsprintname,Method,Sprintname,Variable,FLOWS_TO,variable_transformations
Getsprintname,Method,Sprintname,Variable,USES,variable_transformations
Setsprintname,Method,Sprintname,Variable,FLOWS_TO,variable_transformations
Getcommitedsp,Method,Commitedsp,Variable,FLOWS_TO,variable_transformations
Getcommitedsp,Method,Commitedsp,Variable,USES,variable_transformations
Setcommitedsp,Method,Commitedsp,Variable,FLOWS_TO,variable_transformations
Scorecardsprintdata,Class,Sprintname,Variable,EXPOSES,variable_transformations
Scorecardsprintdata,Class,Commitedsp,Variable,EXPOSES,variable_transformations
Scorecardsprintdata,Class,Issuescommited,Variable,EXPOSES,variable_transformations
Scorecardsprintdata,Class,Issuelist,Class,CONTAINS,variable_transformations
Wid,Variable,Getwid,Method,FLOWS_TO,variable_transformations
Wid,Variable,Setwid,Method,FLOWS_TO,variable_transformations
Getwid,Method,Wid,Variable,PRODUCES,variable_transformations
Getwid,Method,Wid,Variable,USES,variable_transformations
Setwid,Method,Wid,Variable,ACCEPTS,variable_transformations
Prestatewaittime,Variable,Getprestatewaittime,Method,FLOWS_TO,variable_transformations
Prestatewaittime,Variable,Setprestatewaittime,Method,FLOWS_TO,variable_transformations
Getprestatewaittime,Method,Prestatewaittime,Variable,PRODUCES,variable_transformations
Getprestatewaittime,Method,Prestatewaittime,Variable,USES,variable_transformations
Setprestatewaittime,Method,Prestatewaittime,Variable,ACCEPTS,variable_transformations
Createtime,Variable,Getcreatetime,Method,FLOWS_TO,variable_transformations
Createtime,Variable,Setcreatetime,Method,FLOWS_TO,variable_transformations
Getcreatetime,Method,Createtime,Variable,PRODUCES,variable_transformations
Getcreatetime,Method,Createtime,Variable,USES,variable_transformations
Setcreatetime,Method,Createtime,Variable,ACCEPTS,variable_transformations
Effort,Variable,Geteffort,Method,FLOWS_TO,variable_transformations
Effort,Variable,Seteffort,Method,FLOWS_TO,variable_transformations
Geteffort,Method,Effort,Variable,PRODUCES,variable_transformations
Seteffort,Method,Effort,Variable,ACCEPTS,variable_transformations
Leadtime,Variable,Getleadtime,Method,FLOWS_TO,variable_transformations
Leadtime,Variable,Setleadtime,Method,FLOWS_TO,variable_transformations
Getleadtime,Method,Leadtime,Variable,PRODUCES,variable_transformations
Setleadtime,Method,Leadtime,Variable,ACCEPTS,variable_transformations
Crstate,Variable,Getcrstate,Method,FLOWS_TO,variable_transformations
Crstate,Variable,Setcrstate,Method,FLOWS_TO,variable_transformations
Getcrstate,Method,Crstate,Variable,PRODUCES,variable_transformations
Getcrstate,Method,Crstate,Variable,USES,variable_transformations
Setcrstate,Method,Crstate,Variable,ACCEPTS,variable_transformations
Frmstate,Variable,Getfrmstate,Method,FLOWS_TO,variable_transformations
Frmstate,Variable,Setfrmstate,Method,FLOWS_TO,variable_transformations
Getfrmstate,Method,Frmstate,Variable,PRODUCES,variable_transformations
Getfrmstate,Method,Frmstate,Variable,USES,variable_transformations
Setfrmstate,Method,Frmstate,Variable,ACCEPTS,variable_transformations
Mdfdate,Variable,Getmdfdate,Method,FLOWS_TO,variable_transformations
Mdfdate,Variable,Setmdfdate,Method,FLOWS_TO,variable_transformations
Getmdfdate,Method,Mdfdate,Variable,PRODUCES,variable_transformations
Getmdfdate,Method,Mdfdate,Variable,USES,variable_transformations
Setmdfdate,Method,Mdfdate,Variable,ACCEPTS,variable_transformations
Waittime,Variable,Getwaittime,Method,FLOWS_TO,variable_transformations
Waittime,Variable,Setwaittime,Method,FLOWS_TO,variable_transformations
Getwaittime,Method,Waittime,Variable,PRODUCES,variable_transformations
Getwaittime,Method,Waittime,Variable,USES,variable_transformations
Setwaittime,Method,Waittime,Variable,ACCEPTS,variable_transformations
Pname,Variable,Getpname,Method,FLOWS_TO,variable_transformations
Pname,Variable,Setpname,Method,FLOWS_TO,variable_transformations
Getpname,Method,Pname,Variable,PRODUCES,variable_transformations
Setpname,Method,Pname,Variable,ACCEPTS,variable_transformations
Sname,Variable,Getsname,Method,FLOWS_TO,variable_transformations
Sname,Variable,Setsname,Method,FLOWS_TO,variable_transformations
Getsname,Method,Sname,Variable,PRODUCES,variable_transformations
Setsname,Method,Sname,Variable,ACCEPTS,variable_transformations
Projkey,Variable,Getprojkey,Method,FLOWS_TO,variable_transformations
Projkey,Variable,Setprojkey,Method,FLOWS_TO,variable_transformations
Getprojkey,Method,Projkey,Variable,PRODUCES,variable_transformations
Getprojkey,Method,Projkey,Variable,USES,variable_transformations
Setprojkey,Method,Projkey,Variable,ACCEPTS,variable_transformations
Transitionmodel,Class,Transition,Table,WRITES_TO,variable_transformations
Transitionmodel,Class,Transition,Table,READS_FROM,variable_transformations
Workitemarr,Variable,Jsonproperty,Variable,MAPS_TO,variable_transformations
Chartdatacommitted,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdataafter,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdatacompleted,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdatafinalcommited,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdataremoved,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdataeffort,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdatadefects,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartdatacapacity,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Chartissuescomp,Variable,Arraylist<Arraylist<Issuelist>>,Variable,PRODUCES,variable_transformations
Chartissuescomm,Variable,Arraylist<Arraylist<Issuelist>>,Variable,PRODUCES,variable_transformations
Chartissuescommafter,Variable,Arraylist<Arraylist<Issuelist>>,Variable,PRODUCES,variable_transformations
Chartissuesrefined,Variable,Arraylist<Arraylist<Issuelist>>,Variable,PRODUCES,variable_transformations
Chartissuesremoved,Variable,Arraylist<Arraylist<Issuelist>>,Variable,PRODUCES,variable_transformations
Catagories,Variable,Arraylist<String>,Variable,PRODUCES,variable_transformations
Finalcommitedstories,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Finalcommiteddefetcs,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Completedstories,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Completeddefects,Variable,Arraylist<Double>,Variable,PRODUCES,variable_transformations
Startdate,Variable,Arraylist<Long>,Variable,PRODUCES,variable_transformations
Enddate,Variable,Arraylist<Long>,Variable,PRODUCES,variable_transformations
Workitemarr,Variable,Jsonignore,Rule,VALIDATES_AGAINST,variable_transformations
Velocitylist,Class,Jsonproperty,Variable,EXPOSES,variable_transformations
Findbyprojectname,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Deletebyprojectname,Method,Almconfiguration,Table,WRITES_TO,variable_transformations
Save,Method,Almconfiguration,Table,WRITES_TO,variable_transformations
Findbyid,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Findall,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Deletebyid,Method,Almconfiguration,Table,WRITES_TO,variable_transformations
Findbyprojectname,Method,Mongodbqueryhandler,Method,CALLS,variable_transformations
Deletebyprojectname,Method,Mongodbdeletehandler,Method,CALLS,variable_transformations
Findbyid,Method,Mongodbqueryhandler,Method,CALLS,variable_transformations
Deletebyid,Method,Mongodbdeletehandler,Method,CALLS,variable_transformations
Save,Method,Mongodbinserthandler,Method,CALLS,variable_transformations
Findall,Method,Mongodbqueryhandler,Method,CALLS,variable_transformations
Findbyprojectname,Method,Mongodb,Database,QUERIES,variable_transformations
Deletebyprojectname,Method,Mongodb,Database,QUERIES,variable_transformations
Projectname,Variable,Findbyprojectname,Method,ACCEPTS,variable_transformations
Projectname,Variable,Deletebyprojectname,Method,ACCEPTS,variable_transformations
Wid,Variable,List<Changehistorymodel>,Variable,FLOWS_TO,variable_transformations
Projname,Variable,List<Changehistorymodel>,Variable,FLOWS_TO,variable_transformations
Findbywid,Method,Changehistorymodel,Table,READS_FROM,variable_transformations
Findbypname,Method,Changehistorymodel,Table,READS_FROM,variable_transformations
Configurationsettingrep,Class,Findbyprojectname,Method,EXPOSES,variable_transformations
Findbyprojectname,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Configurationsettingrep,Class,Findbyprojectnamein,Method,EXPOSES,variable_transformations
Findbyprojectnamein,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Configurationsettingrep,Class,Deletebyprojectname,Method,EXPOSES,variable_transformations
Deletebyprojectname,Method,Configurationsetting,Table,WRITES_TO,variable_transformations
Configurationsettingrep,Class,Get:/Configuration-Settings?Projectname={Projectname},Endpoint,MAPS_TO,variable_transformations
Findbyprojectname,Method,Projectname,Variable,ACCEPTS,variable_transformations
Findbyprojectname,Method,Projectname,Variable,USES,variable_transformations
Findbyprojectname,Method,Configurationsettings,Variable,RETURNS,variable_transformations
Findbyprojectname,Method,Configurationsettings,Variable,USES,variable_transformations
Configurationsettingrep,Class,Get:/Configuration-Settings?Projectnames={Projectnames},Endpoint,MAPS_TO,variable_transformations
Findbyprojectnamein,Method,Projectnames,Variable,ACCEPTS,variable_transformations
Findbyprojectnamein,Method,Projectnames,Variable,USES,variable_transformations
Findbyprojectnamein,Method,Configurationsettingslist,Variable,RETURNS,variable_transformations
Findbyprojectnamein,Method,Configurationsettingslist,Variable,USES,variable_transformations
Configurationsettingrep,Class,Delete:/Configuration-Settings?Projectname={Projectname},Endpoint,MAPS_TO,variable_transformations
Deletebyprojectname,Method,Projectname,Variable,ACCEPTS,variable_transformations
Deletebyprojectname,Method,Projectname,Variable,USES,variable_transformations
Deletebyprojectname,Method,Deletedcount,Variable,RETURNS,variable_transformations
Deletebyprojectname,Method,Deletedcount,Variable,USES,variable_transformations
Projectname,Variable,Stringnotemptyvalidator,Validator,VALIDATES_WITH,variable_transformations
Projectnames,Variable,Nonemptylistvalidator,Validator,VALIDATES_WITH,variable_transformations
Deletedcount,Variable,Nonnegativerule,Rule,VALIDATES_AGAINST,variable_transformations
Sname,Variable,Findbysnameandpname,Method,FLOWS_TO,variable_transformations
Pname,Variable,Findbysnameandpname,Method,FLOWS_TO,variable_transformations
Sname,Variable,Findbysnameandpnameandpalmtype,Method,FLOWS_TO,variable_transformations
Pname,Variable,Findbysnameandpnameandpalmtype,Method,FLOWS_TO,variable_transformations
Palmtype,Variable,Findbysnameandpnameandpalmtype,Method,FLOWS_TO,variable_transformations
Pname,Variable,Findbypname,Method,FLOWS_TO,variable_transformations
Getsid,Variable,Findbysidandpname,Method,FLOWS_TO,variable_transformations
Projectname,Variable,Findbysidandpname,Method,FLOWS_TO,variable_transformations
Findbysnameandpname,Method,Iterationmodel,Table,READS_FROM,variable_transformations
Findbysnameandpnameandpalmtype,Method,Iterationmodel,Table,READS_FROM,variable_transformations
Findbypname,Method,Iterationmodel,Table,READS_FROM,variable_transformations
Findbysidandpname,Method,Iterationmodel,Table,READS_FROM,variable_transformations
Iterationrepo,Class,Get:/Iterations,Endpoint,EXPOSES,variable_transformations
Findbysnameandpname,Method,Get:/Iterations?Sname={Sname}&Pname={Pname},Endpoint,MAPS_TO,variable_transformations
Findbysnameandpnameandpalmtype,Method,Get:/Iterations?Sname={Sname}&Pname={Pname}&Palmtype={Palmtype},Endpoint,MAPS_TO,variable_transformations
Findbypname,Method,Get:/Iterations?Pname={Pname},Endpoint,MAPS_TO,variable_transformations
Findbysidandpname,Method,Get:/Iterations?Sid={Sid}&Pname={Projectname},Endpoint,MAPS_TO,variable_transformations
Sname,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Pname,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Palmtype,Variable,Optional,Rule,VALIDATES_AGAINST,variable_transformations
Getsid,Variable,Positiveinteger,Rule,VALIDATES_AGAINST,variable_transformations
Projectname,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Findbypnameandsname,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandsname,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbywidandpnameandsname,Method,Metricsmodel,Variable,PRODUCES,variable_transformations
Findbywidandpnameandsname,Method,Metricsmodel,Variable,USES,variable_transformations
Findbywid,Method,Metricsmodel,Variable,PRODUCES,variable_transformations
Findbywid,Method,Metricsmodel,Variable,USES,variable_transformations
Findbypnameandsid,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandsid,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandtypeandpalmtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandtypeandpalmtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandsnameandpalmtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandsnameandpalmtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandsidandpalmtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandsidandpalmtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandsnameandtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandsnameandtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypname,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypname,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandbaselineandtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandbaselineandtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbypnameandwid,Method,Metricsmodel,Variable,PRODUCES,variable_transformations
Findbypnameandwid,Method,Metricsmodel,Variable,USES,variable_transformations
Findbypnameandpalmtype,Method,Metricsmodellist,Variable,PRODUCES,variable_transformations
Findbypnameandpalmtype,Method,Metricsmodellist,Variable,USES,variable_transformations
Findbywidandpname,Method,Metricsmodel,Variable,PRODUCES,variable_transformations
Findbywidandpname,Method,Metricsmodel,Variable,USES,variable_transformations
Findbypnameandsname,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbywidandpnameandsname,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbywid,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandsid,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandtypeandpalmtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandsnameandpalmtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandsidandpalmtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandsnameandtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypname,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandbaselineandtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandwid,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandpalmtype,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbywidandpname,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Findbypnameandsname,Method,Transitionmodel,Table,QUERIES,variable_transformations
Findbywid,Method,Transitionmodel,Table,QUERIES,variable_transformations
Findbypname,Method,Transitionmodel,Table,QUERIES,variable_transformations
Findbypnameandsname,Method,Select,Method,CALLS,variable_transformations
Findbywid,Method,Select,Method,CALLS,variable_transformations
Findbypname,Method,Select,Method,CALLS,variable_transformations
Almconfig,Variable,Environmetkey,Variable,FLOWS_TO,variable_transformations
Customfieldurl,Variable,Customfieldsjsonarray,Variable,FLOWS_TO,variable_transformations
Customfieldsjsonarray,Variable,Customfieldsmap,Variable,TRANSFORMS_TO,variable_transformations
Customfieldsjsonarray,Variable,Customfieldsmapoflpm,Variable,TRANSFORMS_TO,variable_transformations
Projectname,Variable,Almconfig,Variable,FLOWS_TO,variable_transformations
Fieldsofjsondata,Variable,Origest,Variable,FLOWS_TO,variable_transformations
Historyarray,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Iterationset,Variable,Sortediteration,Variable,FLOWS_TO,variable_transformations
Crundate,Variable,Lastcollruntime,Variable,FLOWS_TO,variable_transformations
Originest,Variable,Populatedmetricsdata,Variable,FLOWS_TO,variable_transformations
Init(),Method,Almconfigrepo,Table,READS_FROM,variable_transformations
Deleteiterations(),Method,Iterations,Table,WRITES_TO,variable_transformations
Almconfigrepo,Variable,Almconfig,Variable,FLOWS_TO,variable_transformations
Auth,Variable,Author,Variable,TRANSFORMS_TO,variable_transformations
Metriclist,Variable,Aggregatedmetriclist,Variable,TRANSFORMS_TO,variable_transformations
Getcomponentvelocity,Method,Response,Variable,PRODUCES,variable_transformations
Getcomponentvelocity,Method,Response,Variable,USES,variable_transformations
Getcomponentssprintstories,Method,Response,Variable,PRODUCES,variable_transformations
Getcomponentssprintstories,Method,Response,Variable,USES,variable_transformations
Getissuehierarchy,Method,Resp,Variable,PRODUCES,variable_transformations
Getissuehierarchy,Method,Resp,Variable,USES,variable_transformations
Getsprintwisestories,Method,Sprintwise,Variable,PRODUCES,variable_transformations
Getsprintwisestories,Method,Sprintwise,Variable,USES,variable_transformations
Callsp,Method,Tempsp,Variable,PRODUCES,variable_transformations
Callsp,Method,Tempsp,Variable,USES,variable_transformations
Getvelocitychart,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Getsprintwisestories,Method,Transitionmodel,Table,READS_FROM,variable_transformations
Getcomponentssprintstories,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Chartcalculations,Class,Post:/Api/Velocity,Endpoint,EXPOSES,variable_transformations
Chartcalculations,Class,Get:/Api/Sprint-Stories,Endpoint,EXPOSES,variable_transformations
Getcomponentvelocity,Method,Getvelocitychart,Method,CALLS,variable_transformations
Getcomponentssprintstories,Method,Getcomponentlist,Method,CALLS,variable_transformations
Callsp,Method,Storyloop,Method,CALLS,variable_transformations
Almconfig,Variable,Closestates,Rule,VALIDATES_AGAINST,variable_transformations
Getcomponentvelocity,Method,Velocityread,Permission,AUTHORIZES,variable_transformations
Customfieldsjsonarray,Variable,Customobject,Variable,FLOWS_TO,variable_transformations
Customobject,Variable,Key,Variable,FLOWS_TO,variable_transformations
Customobject,Variable,State,Variable,FLOWS_TO,variable_transformations
State,Variable,Taskstatelist,Variable,FLOWS_TO,variable_transformations
State,Variable,Storystatelist,Variable,FLOWS_TO,variable_transformations
State,Variable,Bugstatelist,Variable,FLOWS_TO,variable_transformations
State,Variable,Epicstatelist,Variable,FLOWS_TO,variable_transformations
Customfieldnames,Class,Getcustomfieldsinfo,Method,EXPOSES,variable_transformations
Customfieldnames,Class,Getprojectstatus,Method,EXPOSES,variable_transformations
Customfieldnames,Class,Getcustomfieldsoflpm,Method,EXPOSES,variable_transformations
Getcustomfieldsinfo,Method,Put,Method,CALLS,variable_transformations
Getprojectstatus,Method,Gettaskstatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getsubtaskstatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getstorystatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getbugstatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getepicstatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getteststatelist,Method,CALLS,variable_transformations
Getprojectstatus,Method,Getstatelist,Method,CALLS,variable_transformations
Gettaskstatelist,Method,Add,Method,CALLS,variable_transformations
Getsubtaskstatelist,Method,Add,Method,CALLS,variable_transformations
Getbugstatelist,Method,Add,Method,CALLS,variable_transformations
Getstorystatelist,Method,Add,Method,CALLS,variable_transformations
Getepicstatelist,Method,Add,Method,CALLS,variable_transformations
Getteststatelist,Method,Add,Method,CALLS,variable_transformations
Getstatelist,Method,Add,Method,CALLS,variable_transformations
Statuscatagoryname,Variable,"Equals(""Done"")",Rule,VALIDATES_AGAINST,variable_transformations
Key,Variable,"Equals(""Sprint"")",Rule,VALIDATES_AGAINST,variable_transformations
Key,Variable,"Equals(""Defect Injector"")",Rule,VALIDATES_AGAINST,variable_transformations
Getcustomfieldsinfo,Method,Customfieldmap,Variable,PRODUCES,variable_transformations
Getcustomfieldsinfo,Method,Customfieldmap,Variable,USES,variable_transformations
Jira_Sprint_Field_Brillio,Variable,Customfieldmap,Variable,PERSISTS_TO,variable_transformations
Getcustomfieldsoflpm,Method,Customfiledsoflpm,Variable,PRODUCES,variable_transformations
Getcustomfieldsoflpm,Method,Customfiledsoflpm,Variable,USES,variable_transformations
Key,Variable,Customfilednamesoflpm,Variable,VALIDATES_AGAINST,variable_transformations
Auth,Variable,Jsonoutput,Variable,PRODUCES,variable_transformations
Allmetrics,Variable,Groupbywid,Variable,FLOWS_TO,variable_transformations
Groupbywid,Variable,Tempmetrics,Variable,FLOWS_TO,variable_transformations
Tempmetrics,Variable,Recentmodel,Variable,FLOWS_TO,variable_transformations
Jsonnew,Variable,Wid,Variable,TRANSFORMS_TO,variable_transformations
Jiraurl,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Username,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Password,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Key,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Recentmodel,Variable,Delete,Method,FLOWS_TO,variable_transformations
Widlist,Variable,Deleteissues,Method,FLOWS_TO,variable_transformations
Handledeletedissues,Method,Metricsmodel,Table,READS_FROM,variable_transformations
Handledeletedissues,Method,Metricsmodel,Table,WRITES_TO,variable_transformations
Handledeletedissues,Method,Metricsrepo,Externalservice,CALLS,variable_transformations
Delete,Method,Metricsmodel,Table,WRITES_TO,variable_transformations
Allmetrics,Variable,Metricsmodel,Table,PERSISTS_TO,variable_transformations
Widlist,Variable,Mongoaggregate,Table,PERSISTS_TO,variable_transformations
Deletejiraissues,Class,Alternatejiraconnection,Method,INVOKES,variable_transformations
Deletejiraissues,Class,Jiraconnection,Method,INVOKES,variable_transformations
Projname,Variable,Groupby,Operation,TRIGGERS,variable_transformations
Startat,Variable,Pagination,Operation,TRIGGERS,variable_transformations
Handledeletedissues,Method,Getcontext,Method,CALLS,variable_transformations
Handledeletedissues,Method,Getbean,Method,CALLS,variable_transformations
Handledeletedissues,Method,Findbypname,Method,CALLS,variable_transformations
Handledeletedissues,Method,Delete,Method,CALLS,variable_transformations
Handledeletedissues,Method,Deleteissues,Method,CALLS,variable_transformations
Key,Variable,Equality ('In'),Businessrule,VALIDATES_AGAINST,variable_transformations
Tempmetrics,Variable,Nonnull Rule,Businessrule,VALIDATES_AGAINST,variable_transformations
Tempmetrics,Variable,Size > 1,Businessrule,VALIDATES_AGAINST,variable_transformations
Projectname,Variable,Pname,Variable,FLOWS_TO,variable_transformations
Sprintname,Variable,Sname,Variable,FLOWS_TO,variable_transformations
"Get(""Field"")",Variable,Field,Variable,TRANSFORMS_TO,variable_transformations
"Get(""To"")",Variable,Newvalue,Variable,TRANSFORMS_TO,variable_transformations
"Get(""From"")",Variable,Oldvalue,Variable,TRANSFORMS_TO,variable_transformations
Timeestimate,Variable,Remainingwork,Variable,FLOWS_TO,variable_transformations
Populatesubarrays,Method,Timestamp,Method,CALLS,variable_transformations
Populatesubarrays,Method,Changeesmt,Variable,TRIGGERS,variable_transformations
Populatesubarrays,Method,Changeesmt,Variable,USES,variable_transformations
Field,Variable,Changehistorytracking,Operation,TRIGGERS,variable_transformations
Timespent,Variable,Efforttracking,Operation,TRIGGERS,variable_transformations
Filteredstatusarray,Variable,Statuschangetracking,Operation,TRIGGERS,variable_transformations
Createddate,Variable,Createddate >= Cruns,Rule,VALIDATES_AGAINST,variable_transformations
"Get(""Field"")",Variable,"Equals(""Timeestimate"")",Rule,VALIDATES_AGAINST,variable_transformations
"Get(""Field"")",Variable,"Equals(""Timespent"")",Rule,VALIDATES_AGAINST,variable_transformations
"Get(""Field"")",Variable,"Equals(""Status"")",Rule,VALIDATES_AGAINST,variable_transformations
Effortmodel,Variable,Efforts,Variable,FLOWS_TO,variable_transformations
Cm,Variable,Changelogs,Variable,FLOWS_TO,variable_transformations
Createddate,Variable,Modifieddatelist,Variable,FLOWS_TO,variable_transformations
Tempissue,Variable,Tempcopy,Variable,FLOWS_TO,variable_transformations
Getepiclink,Method,Key,Variable,PRODUCES,variable_transformations
Getepiclink,Method,Key,Variable,USES,variable_transformations
Tempcopy,Variable,Story,Variable,TRANSFORMS_TO,variable_transformations
Epics,Variable,Child,Variable,FLOWS_TO,variable_transformations
Tempcopy,Variable,Getunmappeddata,Method,FLOWS_TO,variable_transformations
Story,Variable,Group,Variable,FLOWS_TO,variable_transformations
Group,Variable,Storydata,Variable,FLOWS_TO,variable_transformations
Allmetrics,Variable,Tempcopy,Variable,FLOWS_TO,variable_transformations
Childdata,Variable,Tempmetrics,Variable,TRANSFORMS_TO,variable_transformations
Relateddata,Variable,Links,Variable,FLOWS_TO,variable_transformations
Epiclinks,Variable,Epicarray,Variable,FLOWS_TO,variable_transformations
Subtasks,Variable,Subtasklists,Variable,FLOWS_TO,variable_transformations
Mappeddata,Variable,Hierarchyview,Variable,FLOWS_TO,variable_transformations
Linksdata,Variable,Children,Variable,TRANSFORMS_TO,variable_transformations
Unmapped,Variable,Nodelist,Variable,FLOWS_TO,variable_transformations
Gethierarchy,Method,Getunmappeddata,Method,CALLS,variable_transformations
Gethierarchy,Method,Getdatainstructure,Method,CALLS,variable_transformations
Gethierarchy,Method,Getrelatedtaskinfo,Method,CALLS,variable_transformations
Gethierarchy,Method,Getdataintreestructure,Method,CALLS,variable_transformations
Gethierarchy,Method,Getunmappedintreestructure,Method,CALLS,variable_transformations
Gethierarchydata,Method,Gethierarchy,Method,CALLS,variable_transformations
Gettype(),Variable,Typeequalsstoryorepic,Rule,VALIDATES_AGAINST,variable_transformations
Getoutwardissuelink(),Variable,Outwardissuelinknotnull,Rule,VALIDATES_AGAINST,variable_transformations
Json,Variable,Tempkey,Variable,FLOWS_TO,variable_transformations
Customfieldsmap,Variable,Tempkey,Variable,FLOWS_TO,variable_transformations
Tempkey,Variable,Sprintjson,Variable,FLOWS_TO,variable_transformations
Sprintjson,Variable,Sprintdata,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Sprintname,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Sprintstatus,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Startdate,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Enddate,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Completeddate,Variable,FLOWS_TO,variable_transformations
Sprintdata,Variable,Id,Variable,FLOWS_TO,variable_transformations
Sprintstatus,Variable,State,Variable,FLOWS_TO,variable_transformations
Startdate,Variable,Stdate,Variable,FLOWS_TO,variable_transformations
Id,Variable,Sid,Variable,FLOWS_TO,variable_transformations
Iteration,Variable,Iterationset,Variable,FLOWS_TO,variable_transformations
Populateiteration,Method,Getsprintinfo,Method,CALLS,variable_transformations
Populateiteration,Method,Iterationset,Variable,READS_FROM,variable_transformations
Populateiteration,Method,Iterationset,Variable,USES,variable_transformations
Populateiteration,Method,Objectlist,Variable,WRITES_TO,variable_transformations
Response,Variable,Iterationset,Variable,FLOWS_TO,variable_transformations
Iterationset,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Sprintname,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Sprintid,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Sprintset,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Multiplesprints,Variable,Objectlist,Variable,FLOWS_TO,variable_transformations
Getsprintinfo,Method,Split,Method,CALLS,variable_transformations
Populateiteration,Method,Post:/Api/Iteration/Populate,Endpoint,MAPS_TO,variable_transformations
Sname,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
State,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
Stdate,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
Enddate,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
Completeddate,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
Sid,Variable,Iteration,Table,PERSISTS_TO,variable_transformations
Projectname,Variable,Configurationcolection,Variable,FLOWS_TO,variable_transformations
Configurationcolection,Variable,Metric,Variable,FLOWS_TO,variable_transformations
Metric,Variable,Metric1,Variable,FLOWS_TO,variable_transformations
Metric1,Variable,Toolname,Variable,FLOWS_TO,variable_transformations
Metric1,Variable,Instanceurl,Variable,FLOWS_TO,variable_transformations
Metric1,Variable,User,Variable,FLOWS_TO,variable_transformations
Metric1,Variable,Pass,Variable,FLOWS_TO,variable_transformations
Metric1,Variable,Key,Variable,FLOWS_TO,variable_transformations
Instanceurl,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
User,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
Pass,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
Key,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
Toolname,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
Board,Variable,Getalmtooldata,Method,FLOWS_TO,variable_transformations
User,Variable,Pass,Variable,TRANSFORMS_TO,variable_transformations
Metric1,Variable,Instanceurl,Variable,TRANSFORMS_TO,variable_transformations
Jiramain,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Deletejiraissues,Method,Configurationsetting,Table,READS_FROM,variable_transformations
Jiramain,Method,Getcontext,Method,CALLS,variable_transformations
Jiramain,Method,Getalmtooldata,Method,CALLS,variable_transformations
Jiramain,Method,Decrypt,Method,CALLS,variable_transformations
Jiramain,Method,Getlastrun,Method,CALLS,variable_transformations
Jiramain,Method,Getdodashboardmetrics,Method,CALLS,variable_transformations
Jiramain,Method,Cleanobject,Method,CALLS,variable_transformations
Deletejiraissues,Method,Getcontext,Method,CALLS,variable_transformations
Deletejiraissues,Method,Handledeletedissues,Method,CALLS,variable_transformations
Deletejiraissues,Method,Cleanobject,Method,CALLS,variable_transformations
Getalmtooldata,Method,Instanceurl Rest Api,Externalservice,INVOKES,variable_transformations
Getpassword(),Variable,Encryptiondecryptionaes,Validator,VALIDATES_WITH,variable_transformations
Jiramain,Method,User,Variable,AUTHENTICATES,variable_transformations
Jiramain,Method,User,Variable,USES,variable_transformations
Deletejiraissues,Method,User,Variable,AUTHENTICATES,variable_transformations
Deletejiraissues,Method,User,Variable,USES,variable_transformations
Jiramain,Method,Info,Method,CALLS,variable_transformations
Jiramain,Method,Error,Method,CALLS,variable_transformations
Deletejiraissues,Method,Info,Method,CALLS,variable_transformations
Deletejiraissues,Method,Error,Method,CALLS,variable_transformations
Username,Variable,Loginsearch,Variable,FLOWS_TO,variable_transformations
Password,Variable,Loginsearch,Variable,FLOWS_TO,variable_transformations
Loginsearch,Variable,Encodedbytes,Variable,TRANSFORMS_TO,variable_transformations
Encodedbytes,Variable,Logincreds,Variable,TRANSFORMS_TO,variable_transformations
Logincreds,Variable,Basicauth,Variable,FLOWS_TO,variable_transformations
Basicauth,Variable,"Requestproperty(""Authorization"")",Variable,FLOWS_TO,variable_transformations
Key,Variable,Datatj,Variable,FLOWS_TO,variable_transformations
Start,Variable,Datatj,Variable,FLOWS_TO,variable_transformations
Maxresult,Variable,Datatj,Variable,FLOWS_TO,variable_transformations
Lastrundate,Variable,Datatj,Variable,FLOWS_TO,variable_transformations
Datatj,Variable,Makerestcall,Method,FLOWS_TO,variable_transformations
Jiraurl,Variable,Makerestcall,Method,FLOWS_TO,variable_transformations
Jiraauthentication,Class,Get:/Jira/Connection/Status,Endpoint,EXPOSES,variable_transformations
Jiraauthentication,Class,Post:/Jira/Connection,Endpoint,EXPOSES,variable_transformations
Jiraauthentication,Class,Get:/Jira/Board/Filter,Endpoint,EXPOSES,variable_transformations
Jiraconnectionforstatus,Method,Get:/Jira/Connection/Status,Endpoint,MAPS_TO,variable_transformations
Jiraconnection,Method,Post:/Jira/Connection,Endpoint,MAPS_TO,variable_transformations
Getboardfilterid,Method,Get:/Jira/Board/Filter,Endpoint,MAPS_TO,variable_transformations
Get:/Jira/Connection/Status,Endpoint,Jiraurl,Variable,ACCEPTS,variable_transformations
Get:/Jira/Connection/Status,Endpoint,Username,Variable,ACCEPTS,variable_transformations
Get:/Jira/Connection/Status,Endpoint,Password,Variable,ACCEPTS,variable_transformations
Get:/Jira/Connection/Status,Endpoint,Jsonparser,Variable,RETURNS,variable_transformations
Post:/Jira/Connection,Endpoint,Jiraurl,Variable,ACCEPTS,variable_transformations
Post:/Jira/Connection,Endpoint,Username,Variable,ACCEPTS,variable_transformations
Post:/Jira/Connection,Endpoint,Password,Variable,ACCEPTS,variable_transformations
Post:/Jira/Connection,Endpoint,Key,Variable,ACCEPTS,variable_transformations
Post:/Jira/Connection,Endpoint,Responseentity,Variable,RETURNS,variable_transformations
Get:/Jira/Board/Filter,Endpoint,Boardid,Variable,ACCEPTS,variable_transformations
Get:/Jira/Board/Filter,Endpoint,Filterid,Variable,RETURNS,variable_transformations
Loginsearch,Variable,Base64.Encode,Rule,VALIDATES_AGAINST,variable_transformations
Jiraclient,Class,Getalmtooldata,Method,EXPOSES,variable_transformations
Urlalm,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Username,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Password,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Projectname,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Key,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Almtype,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Board,Variable,Getalmtooldata,Method,ACCEPTS,variable_transformations
Getalmtooldata,Method,Projectmodel,Variable,RETURNS,variable_transformations
Getalmtooldata,Method,Projectmodel,Variable,USES,variable_transformations
Getalmtooldata,Method,Jira Rest Api,Externalservice,INVOKES,variable_transformations
Jiraexceptions,Class,Initcause,Method,CALLS,variable_transformations
Json,Variable,Assignee,Variable,FLOWS_TO,variable_transformations
Displayname,Variable,Assgnto,Variable,TRANSFORMS_TO,variable_transformations
Summary,Variable,Summ,Variable,TRANSFORMS_TO,variable_transformations
Fixversionmap,Variable,Fixver,Variable,TRANSFORMS_TO,variable_transformations
Statename,Variable,State/Statuscategory,Variable,TRANSFORMS_TO,variable_transformations
Auth,Variable,Encodedauth,Variable,TRANSFORMS_TO,variable_transformations
Encodedauth,Variable,Authheader,Variable,TRANSFORMS_TO,variable_transformations
Authheader,Variable,Headers,Variable,FLOWS_TO,variable_transformations
Apikey,Variable,Headers,Variable,FLOWS_TO,variable_transformations
Apikey,Variable,Response,Variable,FLOWS_TO,variable_transformations
Getbody(),Variable,Rallyjson,Variable,TRANSFORMS_TO,variable_transformations
Rallyjson,Variable,Feature,Variable,FLOWS_TO,variable_transformations
Feature,Variable,Arrresult,Variable,FLOWS_TO,variable_transformations
Rallyauthentication,Class,Url,Variable,ACCEPTS,variable_transformations
Rallyauthentication,Class,Apikey,Variable,ACCEPTS,variable_transformations
Rallyauthentication,Class,Callrallyurl,Method,EXPOSES,variable_transformations
Callrallyurl,Method,Makerestcallapi,Method,CALLS,variable_transformations
Makerestcallapi,Method,Get,Method,CALLS,variable_transformations
Makerestcallapi,Method,Get:{Url},Externalservice,INVOKES,variable_transformations
Makerestcallapi,Method,Externalservice,Externalservice,AUTHENTICATES,variable_transformations
Apikey,Variable,Apikeynotempty,Rule,VALIDATES_AGAINST,variable_transformations
Response,Variable,Validjsonbody,Rule,VALIDATES_AGAINST,variable_transformations
Releasejson,Variable,Release,Variable,FLOWS_TO,variable_transformations
"Get(""Id"")",Variable,Relid,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Name"")",Variable,Relname,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Released"")",Variable,Released,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Startdate"")",Variable,Stdate,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Releasedate"")",Variable,Reldate,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Userstartdate"")",Variable,Userstdate,Variable,TRANSFORMS_TO,variable_transformations
"Get(""Userreleasedate"")",Variable,Userreldate,Variable,TRANSFORMS_TO,variable_transformations
Almconfigrepo,Variable,Almconfiguration,Variable,FLOWS_TO,variable_transformations
Metricslist,Variable,Metricsmodel,Variable,FLOWS_TO,variable_transformations
Entryset(),Variable,Entry,Variable,FLOWS_TO,variable_transformations
Entryset(),Variable,Xmap,Variable,FLOWS_TO,variable_transformations
Getreleasedetails,Method,Almconfig,Table,READS_FROM,variable_transformations
Getreleasedetails,Method,Release,Table,READS_FROM,variable_transformations
Getreleasedetails,Method,Releasedetails,Table,WRITES_TO,variable_transformations
Getreleasestorycount,Method,Metrics,Table,READS_FROM,variable_transformations
Save,Method,Releasedetails,Table,WRITES_TO,variable_transformations
Getreleasedetails,Method,Getreleasestorycount,Method,CALLS,variable_transformations
Getreleasedetails,Method,Findbyprojectname,Method,CALLS,variable_transformations
Getreleasedetails,Method,Save,Method,CALLS,variable_transformations
Getreleasedetails,Method,Cleanobject,Method,CALLS,variable_transformations
Getreleasedetails,Method,Gettime,Method,CALLS,variable_transformations
Gettime,Method,Dateparsing,Businessrule,PROCESSES,variable_transformations
Getreleasestorycount,Method,Getclosestate,Method,CALLS,variable_transformations
Getreleasestorycount,Method,Findbypnameandpalmtype,Method,CALLS,variable_transformations
Time,Variable,Dateformatrule,Rule,VALIDATES_AGAINST,variable_transformations
Getstate(),Variable,Almconfiguration.Getclosestate,Rule,VALIDATES_AGAINST,variable_transformations
Projectname,Variable,Almconfigrepo.Projectexists,Rule,VALIDATES_AGAINST,variable_transformations
Gettimezone(),Variable,Timezonerule,Rule,VALIDATES_AGAINST,variable_transformations
Pname,Variable,Projectname,Variable,FLOWS_TO,variable_transformations
Configuration,Variable,Closedstate,Variable,FLOWS_TO,variable_transformations
Configuration,Variable,Defectname,Variable,FLOWS_TO,variable_transformations
Metricslist,Variable,Tasklist,Variable,FLOWS_TO,variable_transformations
Tasklist,Variable,Objectarray,Variable,FLOWS_TO,variable_transformations
Objectarray,Variable,Metric,Variable,FLOWS_TO,variable_transformations
Cqlist,Variable,Metrics,Variable,FLOWS_TO,variable_transformations
Getsprintdata,Method,Iteration,Table,READS_FROM,variable_transformations
Getsprintdata,Method,Almconfiguration,Table,READS_FROM,variable_transformations
Getplannedstorypoint,Method,Metric,Table,READS_FROM,variable_transformations
Getteamsize,Method,Metric,Table,READS_FROM,variable_transformations
Getbuildfailure,Method,Buildtool,Table,READS_FROM,variable_transformations
Gettd,Method,Codequality,Table,READS_FROM,variable_transformations
Getdefectssev,Method,Metric,Table,READS_FROM,variable_transformations
Storyeffortcalculation,Method,Metric,Table,READS_FROM,variable_transformations
Getplannedstorypoint,Method,Iteration,Table,WRITES_TO,variable_transformations
Getteamsize,Method,Iteration,Table,WRITES_TO,variable_transformations
Getbuildfailure,Method,Iteration,Table,WRITES_TO,variable_transformations
Gettd,Method,Iteration,Table,WRITES_TO,variable_transformations
Getdefectssev,Method,Project,Table,WRITES_TO,variable_transformations
Storyeffortcalculation,Method,Metric,Table,WRITES_TO,variable_transformations
Sprintwisecalculation,Class,Getsprintdata,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Getteamsize,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Getplannedstorypoint,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Getbuildfailure,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Gettd,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Getcrtitr,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Getdefectssev,Method,EXPOSES,variable_transformations
Sprintwisecalculation,Class,Storyeffortcalculation,Method,EXPOSES,variable_transformations
Getsprintdata,Method,Getteamsize,Method,CALLS,variable_transformations
Getsprintdata,Method,Getplannedstorypoint,Method,CALLS,variable_transformations
Getsprintdata,Method,Gettd,Method,CALLS,variable_transformations
Getsprintdata,Method,Storyeffortcalculation,Method,CALLS,variable_transformations
Getplannedstorypoint,Method,Gettotaleffort,Method,CALLS,variable_transformations
Storyeffortcalculation,Method,Gettotaleffort,Method,CALLS,variable_transformations
Tasklist,Variable,Null Check,Rule,VALIDATES_AGAINST,variable_transformations
Closedstate,Variable,Contains Check,Rule,VALIDATES_AGAINST,variable_transformations
Metricslist,Variable,Iteration Check,Rule,VALIDATES_AGAINST,variable_transformations
Filteredstatusarray,Variable,Filteredstatusjsonobject,Variable,FLOWS_TO,variable_transformations
Filteredstatusjsonobject,Variable,Fromstate,Variable,FLOWS_TO,variable_transformations
Filteredstatusjsonobject,Variable,Tostate,Variable,FLOWS_TO,variable_transformations
Tostate,Variable,Crstate,Variable,TRANSFORMS_TO,variable_transformations
Fromstate,Variable,Frmstate,Variable,TRANSFORMS_TO,variable_transformations
Modifieddate,Variable,Mdfdate,Variable,TRANSFORMS_TO,variable_transformations
Getfilteredstatusarray,Method,Filteredstatusarray,Variable,PRODUCES,variable_transformations
Getfilteredstatusarray,Method,Filteredstatusarray,Variable,USES,variable_transformations
Getmodifieddatelist,Method,Modifieddatelist,Variable,PRODUCES,variable_transformations
Getmodifieddatelist,Method,Modifieddatelist,Variable,USES,variable_transformations
Getlaststate,Method,Tasklaststate,Variable,PRODUCES,variable_transformations
Getlaststate,Method,Tasklaststate,Variable,USES,variable_transformations
Geteffort,Method,Taskeffort,Variable,PRODUCES,variable_transformations
Geteffort,Method,Taskeffort,Variable,USES,variable_transformations
Getcrtime,Method,Creationtime,Variable,PRODUCES,variable_transformations
Getcrtime,Method,Creationtime,Variable,USES,variable_transformations
Getfirststate,Method,Firststate,Variable,PRODUCES,variable_transformations
Getfirststate,Method,Firststate,Variable,USES,variable_transformations
Gettaskdetailslist,Method,Taskdetailslist,Variable,PRODUCES,variable_transformations
Gettaskdetailslist,Method,Taskdetailslist,Variable,USES,variable_transformations
Populatetransition,Method,Getfilteredstatusarray,Method,CALLS,variable_transformations
Populatetransition,Method,Getmodifieddatelist,Method,CALLS,variable_transformations
Populatetransition,Method,Getwid,Method,CALLS,variable_transformations
Populatetransition,Method,Getlaststate,Method,CALLS,variable_transformations
Populatetransition,Method,Geteffort,Method,CALLS,variable_transformations
Populatetransition,Method,Getcrtime,Method,CALLS,variable_transformations
Populatetransition,Method,Getfirststate,Method,CALLS,variable_transformations
Populatetransition,Method,Gettaskdetailslist,Method,CALLS,variable_transformations
Populatetransition,Method,Getpname,Method,CALLS,variable_transformations
Populatetransition,Method,Getsname,Method,CALLS,variable_transformations
Populatetransition,Method,Timestamp,Method,CALLS,variable_transformations
Filteredstatusarray,Variable,Isempty,Rule,VALIDATES_AGAINST,variable_transformations
Taskdetails,Variable,Taskdetailslist,Variable,PERSISTS_TO,variable_transformations
Filteredstatusarray,Variable,Filteredstatusjsonobject,Variable,TRANSFORMS_TO,variable_transformations
Filteredstatusjsonobject,Variable,Fromstate,Variable,TRANSFORMS_TO,variable_transformations
Filteredstatusjsonobject,Variable,Tostate,Variable,TRANSFORMS_TO,variable_transformations
Filteredstatusarray,Variable,Emptyarray,Rule,VALIDATES_AGAINST,variable_transformations
Setprojkey,Method,Projkey,Variable,CALLS,variable_transformations
Getprojkey,Method,Projkey,Variable,CALLS,variable_transformations
Setfilteredstatusarray,Method,Filteredstatusarray,Variable,CALLS,variable_transformations
Getfilteredstatusarray,Method,Filteredstatusarray,Variable,CALLS,variable_transformations
Setmodifieddatelist,Method,Modifieddatelist,Variable,CALLS,variable_transformations
Getmodifieddatelist,Method,Modifieddatelist,Variable,CALLS,variable_transformations
Setwid,Method,Wid,Variable,CALLS,variable_transformations
Getwid,Method,Wid,Variable,CALLS,variable_transformations
Setlaststate,Method,Laststate,Variable,CALLS,variable_transformations
Getlaststate,Method,Laststate,Variable,CALLS,variable_transformations
Getlaststate,Method,Laststate,Variable,USES,variable_transformations
Setcrtime,Method,Crtime,Variable,CALLS,variable_transformations
Getcrtime,Method,Crtime,Variable,CALLS,variable_transformations
Getcrtime,Method,Crtime,Variable,USES,variable_transformations
Setfirststate,Method,Firststate,Variable,CALLS,variable_transformations
Getfirststate,Method,Firststate,Variable,CALLS,variable_transformations
Settaskdetailslist,Method,Taskdetailslist,Variable,CALLS,variable_transformations
Gettaskdetailslist,Method,Taskdetailslist,Variable,CALLS,variable_transformations
Projectname,Variable,Getinititialdetails,Method,FLOWS_TO,variable_transformations
Almconfiguration,Variable,Closestates,Variable,FLOWS_TO,variable_transformations
Findbyprojectname,Method,Almconfiguration,Variable,PRODUCES,variable_transformations
Findbyprojectname,Method,Almconfiguration,Variable,USES,variable_transformations
Getcomponents,Method,Componentlists,Variable,PRODUCES,variable_transformations
Getcomponents,Method,Componentlists,Variable,USES,variable_transformations
Componentlists,Variable,Stories,Variable,TRANSFORMS_TO,variable_transformations
Stories,Variable,Storyinsightlist,Variable,TRANSFORMS_TO,variable_transformations
Metricagedata,Variable,Storyinsightlist,Variable,TRANSFORMS_TO,variable_transformations
Gettransitions(),Variable,Transitions,Variable,FLOWS_TO,variable_transformations
Getstorypoints(),Variable,Sps,Variable,FLOWS_TO,variable_transformations
Age,Variable,Agedisplay,Variable,TRANSFORMS_TO,variable_transformations
Getissueworkflowsprint,Variable,Pushstateflowobject2,Method,FLOWS_TO,variable_transformations
Findbyprojectname,Method,Almconfig,Table,READS_FROM,variable_transformations
Findbypname,Method,Iterationoutmodel,Table,READS_FROM,variable_transformations
Getinititialdetails,Method,Findbyprojectname,Method,CALLS,variable_transformations
Getinititialdetails,Method,Findbypname,Method,CALLS,variable_transformations
Getinititialdetails,Method,Mongotemplate,Method,CALLS,variable_transformations
Calculatestoryageing2,Method,Getinititialdetails,Method,CALLS,variable_transformations
Calculatestoryageing,Method,Getinititialdetails,Method,CALLS,variable_transformations
Calculatestoryageing,Method,Pushstateflowobject,Method,CALLS,variable_transformations
Calculatestoryageing2,Method,Pushstateflowobject2,Method,CALLS,variable_transformations
Calculatestoryageing2,Method,Getcomponents,Method,CALLS,variable_transformations
Calculategroomingtable,Method,Findbypnameandpalmtype,Method,CALLS,variable_transformations
Calculategroomingtable,Method,Getcomponents,Method,CALLS,variable_transformations
Getclosestate(),Variable,Closestates,Variable,VALIDATES_AGAINST,variable_transformations
Getstate(),Variable,Closestates,Variable,VALIDATES_AGAINST,variable_transformations
Calculatestoryageing2,Method,Pushstateflowobject2,Method,VALIDATES_WITH,variable_transformations
Calculatestoryageing,Method,Pushstateflowobject,Method,VALIDATES_WITH,variable_transformations
Backlogcalculation,Class,Pushstateflowobject,Method,CALLS,variable_transformations
Backlogcalculation,Class,Calculatestoryageing2,Method,CALLS,variable_transformations
Backlogcalculation,Class,Findbypnameandpalmtype,Method,CALLS,variable_transformations
Builddata,Variable,Builddatalist,Variable,FLOWS_TO,variable_transformations
Buildsteparrayresult,Variable,Groupedbysteps,Variable,FLOWS_TO,variable_transformations
Getstepslist(),Variable,Steps,Variable,FLOWS_TO,variable_transformations
Getstepname(),Variable,Name,Variable,TRANSFORMS_TO,variable_transformations
Successcount,Variable,Avgsuccessrate,Variable,FLOWS_TO,variable_transformations
Duration,Variable,Avgduration,Variable,FLOWS_TO,variable_transformations
Mttrcount,Variable,Avgmttr,Variable,TRANSFORMS_TO,variable_transformations
Mttrduration,Variable,Avgmttr,Variable,TRANSFORMS_TO,variable_transformations
Lastbuild,Variable,Lastbuildstate,Variable,FLOWS_TO,variable_transformations
Getmetrics(),Variable,Groupbyvaluestream,Variable,FLOWS_TO,variable_transformations
Getjobname(),Variable,Getvaluestreamname()],Variable,FLOWS_TO,variable_transformations
Groupbyvaluestream,Variable,Joblist,Variable,FLOWS_TO,variable_transformations
Jobs,Variable,Substeps,Variable,FLOWS_TO,variable_transformations
Findbyname,Method,Buildtool,Table,READS_FROM,variable_transformations
Findbynameandreponameandbranchname,Method,Buildtool,Table,READS_FROM,variable_transformations
Findbyprojectname,Method,Jobdetails,Table,READS_FROM,variable_transformations
Find,Method,Buildtool,Table,READS_FROM,variable_transformations
Valuestreamstep,Variable,Response,Variable,PERSISTS_TO,variable_transformations
Preparepipeline,Method,Jobdetails,Table,READS_FROM,variable_transformations
Buildcalculations,Class,Get:/Api/Value-Stream-Steps,Endpoint,EXPOSES,variable_transformations
Buildcalculations,Class,Get:/Api/Gitlab-Value-Stream-Steps,Endpoint,EXPOSES,variable_transformations
Buildcalculations,Method,Getcontext,Method,CALLS,variable_transformations
Buildcalculations,Method,Getinstance,Method,CALLS,variable_transformations
Getvaluestreampipelinejobs,Method,Preparepipeline,Method,CALLS,variable_transformations
Getvaluestreampipelinejobs,Method,Preparejoblist,Method,CALLS,variable_transformations
Preparejoblist,Method,Mongotemplate.Find,Externalservice,CALLS,variable_transformations
Preparepipeline,Method,Getmetrics,Method,CALLS,variable_transformations
Calculatechildsteps,Method,Copyproperties,Method,CALLS,variable_transformations
Reponame,Variable,Notnull,Rule,VALIDATES_AGAINST,variable_transformations
Getstepslist(),Variable,Notnull,Rule,VALIDATES_AGAINST,variable_transformations
Authorlist,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Get(0),Variable,Component,Variable,FLOWS_TO,variable_transformations
Component,Variable,Componentlist,Variable,FLOWS_TO,variable_transformations
Time,Variable,Seconds,Variable,TRANSFORMS_TO,variable_transformations
Seconds,Variable,Minutes,Variable,TRANSFORMS_TO,variable_transformations
Minutes,Variable,Hours,Variable,TRANSFORMS_TO,variable_transformations
Hours,Variable,Days,Variable,TRANSFORMS_TO,variable_transformations
Days,Variable,Week,Variable,TRANSFORMS_TO,variable_transformations
Milis,Variable,Seconds,Variable,TRANSFORMS_TO,variable_transformations
Seconds,Variable,Minutes,Variable,FLOWS_TO,variable_transformations
Minutes,Variable,Hours,Variable,FLOWS_TO,variable_transformations
Hours,Variable,Days,Variable,FLOWS_TO,variable_transformations
Seconds,Variable,Hours,Variable,TRANSFORMS_TO,variable_transformations
Getcomponentlist,Method,Getmetrics,Method,CALLS,variable_transformations
Getcomponentlist,Method,Getcomponents,Method,CALLS,variable_transformations
Converttodisplayvalues,Method,Time,Variable,PROCESSES,variable_transformations
Converttodisplayvalues,Method,Time,Variable,USES,variable_transformations
Converttodisplayvalues,Method,Noofdays,Variable,PROCESSES,variable_transformations
Converttodisplayvalues,Method,Noofdays,Variable,USES,variable_transformations
Convertmilistodisplayvaluesdefect,Method,Milis,Variable,PROCESSES,variable_transformations
Convertmilistodisplayvaluesdefect,Method,Milis,Variable,USES,variable_transformations
Convertmilistodisplayvaluesdefect,Method,Noofdays,Variable,PROCESSES,variable_transformations
Convertmilistodisplayvaluesdefect,Method,Noofdays,Variable,USES,variable_transformations
Convertsecondstostringdisplay,Method,Seconds,Variable,PROCESSES,variable_transformations
Convertsecondstostringdisplay,Method,Seconds,Variable,USES,variable_transformations
Convertsecondstostringdisplay,Method,Noofdays,Variable,PROCESSES,variable_transformations
Convertsecondstostringdisplay,Method,Noofdays,Variable,USES,variable_transformations
Tohoursstring,Method,Seconds,Variable,PROCESSES,variable_transformations
Tohoursstring,Method,Seconds,Variable,USES,variable_transformations
Todaysstring,Method,Milis,Variable,PROCESSES,variable_transformations
Todaysstring,Method,Milis,Variable,USES,variable_transformations
Todaysstring,Method,Noofdays,Variable,PROCESSES,variable_transformations
Todaysstring,Method,Noofdays,Variable,USES,variable_transformations
Prodtypeprior,Variable,Prodsev,Variable,FLOWS_TO,variable_transformations
Prodtypesev,Variable,Prodsev,Variable,FLOWS_TO,variable_transformations
Prodtypenewdef,Variable,Prodsev,Variable,FLOWS_TO,variable_transformations
Prodtypewaitdef,Variable,Prodsev,Variable,FLOWS_TO,variable_transformations
Cryptoutils,Class,Getrandomnonce,Method,EXPOSES,variable_transformations
Cryptoutils,Class,Getaeskey,Method,EXPOSES,variable_transformations
Cryptoutils,Class,Getaeskeyfrompassword,Method,EXPOSES,variable_transformations
Cryptoutils,Class,Hex,Method,EXPOSES,variable_transformations
Cryptoutils,Class,Hexwithblocksize,Method,EXPOSES,variable_transformations
Getrandomnonce,Method,Nonce,Variable,PRODUCES,variable_transformations
Numbytes,Variable,Getrandomnonce,Method,FLOWS_TO,variable_transformations
Getrandomnonce,Method,Nextbytes,Method,CALLS,variable_transformations
Getaeskey,Method,Secretkey,Variable,PRODUCES,variable_transformations
Getaeskey,Method,Secretkey,Variable,USES,variable_transformations
Keysize,Variable,Getaeskey,Method,FLOWS_TO,variable_transformations
Getaeskey,Method,Getinstance,Method,CALLS,variable_transformations
Getaeskey,Method,Getinstancestrong,Method,CALLS,variable_transformations
Getaeskey,Method,Init,Method,CALLS,variable_transformations
Getaeskey,Method,Generatekey,Method,CALLS,variable_transformations
Getaeskeyfrompassword,Method,Secretkey,Variable,PRODUCES,variable_transformations
Getaeskeyfrompassword,Method,Secretkey,Variable,USES,variable_transformations
Password,Variable,Getaeskeyfrompassword,Method,FLOWS_TO,variable_transformations
Salt,Variable,Getaeskeyfrompassword,Method,FLOWS_TO,variable_transformations
Password,Variable,Pbekeyspec,Validator,VALIDATES_WITH,variable_transformations
Getaeskeyfrompassword,Method,Getinstance,Method,CALLS,variable_transformations
Getaeskeyfrompassword,Method,Generatesecret,Method,CALLS,variable_transformations
Getaeskeyfrompassword,Method,Getencoded,Method,CALLS,variable_transformations
Getaeskeyfrompassword,Method,Aeskey,Variable,TRANSFORMS_TO,variable_transformations
Getaeskeyfrompassword,Method,Aeskey,Variable,USES,variable_transformations
Hex,Method,Hexstring,Variable,TRANSFORMS_TO,variable_transformations
Hex,Method,Hexstring,Variable,USES,variable_transformations
Bytes,Variable,Hex,Method,FLOWS_TO,variable_transformations
Hex,Method,Append,Method,CALLS,variable_transformations
Hex,Method,Format,Method,CALLS,variable_transformations
Hexwithblocksize,Method,Formattedhex,Variable,TRANSFORMS_TO,variable_transformations
Hexwithblocksize,Method,Formattedhex,Variable,USES,variable_transformations
Bytes,Variable,Hexwithblocksize,Method,FLOWS_TO,variable_transformations
Blocksize,Variable,Hexwithblocksize,Method,FLOWS_TO,variable_transformations
Hexwithblocksize,Method,Hex,Method,CALLS,variable_transformations
Hexwithblocksize,Method,Substring,Method,CALLS,variable_transformations
Hexwithblocksize,Method,Add,Method,CALLS,variable_transformations
Formattedhex,Variable,Blocksizetransformation,Rule,VALIDATES_WITH,variable_transformations
Context,Variable,Almconfigrepo,Variable,FLOWS_TO,variable_transformations
Almconfiguration,Variable,Almtype,Variable,FLOWS_TO,variable_transformations
Getclosestate(),Variable,Closestates,Variable,TRANSFORMS_TO,variable_transformations
Iterations,Variable,Itermetricsmeta Linear,Variable,FLOWS_TO,variable_transformations
Encrypteddata,Variable,Decrypt,Method,FLOWS_TO,variable_transformations
Ptext,Variable,Encrypt,Method,FLOWS_TO,variable_transformations
Encrypt,Method,Encryptedstring,Variable,PRODUCES,variable_transformations
Encrypt,Method,Encryptedstring,Variable,USES,variable_transformations
Decrypt,Method,Decryptedstring,Variable,PRODUCES,variable_transformations
Decrypt,Method,Decryptedstring,Variable,USES,variable_transformations
Encrypt,Method,Info,Method,CALLS,variable_transformations
Encrypt,Method,Error,Method,CALLS,variable_transformations
Decrypt,Method,Info,Method,CALLS,variable_transformations
Decrypt,Method,Error,Method,CALLS,variable_transformations
Encrypteddata,Variable,Notempty,Rule,VALIDATES_AGAINST,variable_transformations
Encrypt,Method,Lengthcheck,Rule,VALIDATES_WITH,variable_transformations
Encrypt,Method,Encryptoraesgcmpassword,Externalservice,INVOKES,variable_transformations
Decrypt,Method,Encryptoraesgcmpassword,Externalservice,INVOKES,variable_transformations
Ptext,Variable,Ciphertext,Variable,FLOWS_TO,variable_transformations
Salty,Variable,Aeskeyfrompassword,Variable,TRANSFORMS_TO,variable_transformations
Ciphertext,Variable,Ciphertextwithivsalt,Variable,FLOWS_TO,variable_transformations
Decode,Variable,Ciphertext,Variable,TRANSFORMS_TO,variable_transformations
Password,Variable,Aeskeyfrompassword,Variable,TRANSFORMS_TO,variable_transformations
Decode,Variable,Iv,Variable,TRANSFORMS_TO,variable_transformations
Decode,Variable,Salt,Variable,TRANSFORMS_TO,variable_transformations
Decode,Variable,Plaintext,Variable,TRANSFORMS_TO,variable_transformations
Encrypt,Method,Ciphertextwithivsalt,Variable,PRODUCES,variable_transformations
Encrypt,Method,Ciphertext,Variable,PRODUCES,variable_transformations
Decrypt,Method,Plaintext,Variable,PRODUCES,variable_transformations
Decrypt,Method,Decode,Variable,PRODUCES,variable_transformations
Encrypt,Method,Getrandomnonce,Method,CALLS,variable_transformations
Encrypt,Method,Getaeskeyfrompassword,Method,CALLS,variable_transformations
Encrypt,Method,Getinstance,Method,CALLS,variable_transformations
Encrypt,Method,Init,Method,CALLS,variable_transformations
Decrypt,Method,Getaeskeyfrompassword,Method,CALLS,variable_transformations
Decrypt,Method,Getinstance,Method,CALLS,variable_transformations
Decrypt,Method,Init,Method,CALLS,variable_transformations
Salty,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Ptext,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Password,Variable,Nonnull,Rule,VALIDATES_AGAINST,variable_transformations
Encrypt,Method,Cryptoutils,Validator,VALIDATES_WITH,variable_transformations
Decrypt,Method,Cryptoutils,Validator,VALIDATES_WITH,variable_transformations
Userid,Variable,Auth,Variable,FLOWS_TO,variable_transformations
Createbasicauthheaders,Method,Headers,Variable,PRODUCES,variable_transformations
Headers,Variable,Makegetrestcall,Method,FLOWS_TO,variable_transformations
Authstring,Variable,Authencbytes,Variable,FLOWS_TO,variable_transformations
Authencbytes,Variable,Authstringenc,Variable,TRANSFORMS_TO,variable_transformations
Restclient,Class,Get:/Api/Any,Endpoint,EXPOSES,variable_transformations
Makegetrestcall,Method,Get:/Api/Any,Endpoint,MAPS_TO,variable_transformations
Get:/Api/Any,Endpoint,Url,Variable,ACCEPTS,variable_transformations
Get:/Api/Any,Endpoint,Responseentity<String>,Variable,RETURNS,variable_transformations
Makegetrestcall,Method,Createbasicauthheaders,Method,CALLS,variable_transformations
Makegetrestcall,Method,Get,Method,CALLS,variable_transformations
Get,Method,Resttemplate,Variable,PRODUCES,variable_transformations
Get,Method,Resttemplate,Variable,USES,variable_transformations
Downloadxml,Method,Unzipit,Method,CALLS,variable_transformations
Cleanup,Method,Deletedirectory,Method,CALLS,variable_transformations
Unzipit,Method,Zipinputstream,Externalservice,CALLS,variable_transformations
Unzipit,Method,Fileoutputstream,Externalservice,CALLS,variable_transformations
Encodedauth,Variable,Base64 Non-Empty,Rule,VALIDATES_AGAINST,variable_transformations
Createbasicauthheaders,Method,Basicauthheader,Permission,AUTHORIZES,variable_transformations
Makegetrestcall,Method,Username/Password,Variable,AUTHENTICATES,variable_transformations
Makegetrestcall,Method,Username/Password,Variable,USES,variable_transformations
Is,Variable,Zip,Variable,PERSISTS_TO,variable_transformations
Zip,Variable,Unzipit,Method,FLOWS_TO,variable_transformations
Zip,Variable,Unzippedfiles,Variable,TRANSFORMS_TO,variable_transformations
Unzippedfiles,Variable,Test,Variable,PERSISTS_TO,variable_transformations
Cleanup,Method,Test,Variable,PERSISTS_TO,variable_transformations
Cleanup,Method,Test,Variable,USES,variable_transformations
Cleanup,Method,Zip,Variable,PERSISTS_TO,variable_transformations
Makegetrestcall,Method,Info,Method,CALLS,variable_transformations
Makegetrestcall,Method,Error,Method,CALLS,variable_transformations
Downloadxml,Method,Error,Method,CALLS,variable_transformations
Unzipit,Method,Error,Method,CALLS,variable_transformations
Metric,Variable,Metricval,Variable,TRANSFORMS_TO,variable_transformations
Getmetrics(),Variable,Metrics,Variable,FLOWS_TO,variable_transformations
Componentlist,Variable,Sortedcomponentlist,Variable,TRANSFORMS_TO,variable_transformations
Authordata,Variable,Filteredauthordata,Variable,FLOWS_TO,variable_transformations
Storypoints,Variable,Remainingsp,Variable,FLOWS_TO,variable_transformations
Closedissues,Variable,Completionpercentage,Variable,TRANSFORMS_TO,variable_transformations
Totalspent,Variable,Charteffor,Variable,FLOWS_TO,variable_transformations
Getinitialdata,Method,Projectiterationrepo,Table,READS_FROM,variable_transformations
Getinitialdata,Method,Almconfigrepo,Table,READS_FROM,variable_transformations
Sprintprogress,Class,Get:/Api/Sprint/Risk,Endpoint,EXPOSES,variable_transformations
Gettaskrisk,Method,Getinitialdata,Method,CALLS,variable_transformations
Gettaskrisk,Method,Getissueriskstorypoint,Method,CALLS,variable_transformations
Gettaskrisk,Method,Getissueriskeffortbased,Method,CALLS,variable_transformations
Getburndown,Method,Getinitialdata,Method,CALLS,variable_transformations
Getburndown,Method,Getburndownstorypoint,Method,CALLS,variable_transformations
Getburndown,Method,Getburndowneffort,Method,CALLS,variable_transformations
Getissueriskeffortbased,Method,Setrequiredvalues,Method,CALLS,variable_transformations
Getissueriskstorypoint,Method,Getlateststorypoints,Method,CALLS,variable_transformations
Getissueriskstorypoint,Method,Getissueriskstatus,Method,CALLS,variable_transformations
Getissueriskeffortbased,Method,Getissueriskstatus,Method,CALLS,variable_transformations
Getburndowneffort,Method,Isallocated,Method,CALLS,variable_transformations
Getburndowneffort,Method,Isremoved,Method,CALLS,variable_transformations
Getissueriskstatus,Method,Calculationlogic,Rule,VALIDATES_AGAINST,variable_transformations
Getinitialdata,Method,Validator:Almconfigrepo,Validator,VALIDATES_WITH,variable_transformations
Ctx,Variable,Almconfigrepo,Variable,PRODUCES,variable_transformations
Ctx,Variable,Metricrepo,Variable,PRODUCES,variable_transformations
Mongo,Variable,Alliterations,Variable,FLOWS_TO,variable_transformations
Metrics,Variable,Groupedbytype,Variable,FLOWS_TO,variable_transformations
Groupedbytype,Variable,Sprints,Variable,FLOWS_TO,variable_transformations
Groupedbytype,Variable,States,Variable,FLOWS_TO,variable_transformations
Sprints,Variable,Issuebysprintandtype,Variable,FLOWS_TO,variable_transformations
States,Variable,Countmap,Variable,FLOWS_TO,variable_transformations
Spdata,Variable,Componentlist,Variable,FLOWS_TO,variable_transformations
Componentlist,Variable,Response,Variable,PRODUCES,variable_transformations
Almconfigrepo,Variable,Almconfig,Variable,PRODUCES,variable_transformations
Almconfig,Variable,Storydata,Variable,TRANSFORMS_TO,variable_transformations
Almconfig,Variable,Iterationdata,Variable,TRANSFORMS_TO,variable_transformations
Getinititialdetails,Method,Metrics,Table,READS_FROM,variable_transformations
Getinititialdetails,Method,Iterations,Table,READS_FROM,variable_transformations
Getstoryprogress,Method,Metrics,Table,READS_FROM,variable_transformations
Getstoryprogress,Method,Iterations,Table,READS_FROM,variable_transformations
Allissues,Variable,Metrics,Table,PERSISTS_TO,variable_transformations
Alliterations,Variable,Iterations,Table,PERSISTS_TO,variable_transformations
Iterationdata,Variable,Iterations,Table,PERSISTS_TO,variable_transformations
Storydata,Variable,Metrics,Table,PERSISTS_TO,variable_transformations
Metricstaskdata,Variable,Metrics,Table,PERSISTS_TO,variable_transformations
Sprintprogresscalculations,Class,Get:/Api/Sprints/Progress,Endpoint,EXPOSES,variable_transformations
Getissuebreakup,Method,Get:/Api/Sprints/Breakup,Endpoint,MAPS_TO,variable_transformations
Get:/Api/Sprints/Breakup,Endpoint,Projectname,Variable,ACCEPTS,variable_transformations
Get:/Api/Sprints/Breakup,Endpoint,Almtype,Variable,ACCEPTS,variable_transformations
Get:/Api/Sprints/Breakup,Endpoint,Response,Variable,RETURNS,variable_transformations
Getstoryprogress,Method,Get:/Api/Sprints/Story-Progress,Endpoint,MAPS_TO,variable_transformations
Get:/Api/Sprints/Story-Progress,Endpoint,Response,Variable,RETURNS,variable_transformations
Sprintprogresscalculations,Method,Getinititialdetails,Method,CALLS,variable_transformations
Getinititialdetails,Method,Getbean,Method,CALLS,variable_transformations
Getissuebreakup,Method,Getinititialdetails,Method,CALLS,variable_transformations
Getissuebreakup,Method,Getcomponents,Method,CALLS,variable_transformations
Getissuebreakup,Method,Groupbycomponent,Businessrule,PROCESSES,variable_transformations
Getstoryprogress,Method,Getcomponents,Method,CALLS,variable_transformations
Getstoryprogress,Method,Calloperation,Method,CALLS,variable_transformations
Getstoryprogress,Method,Storypointsuccesscalculation,Businessrule,PROCESSES,variable_transformations
Allissues,Variable,"Statenotin(""Backlog"", ""Backlog"", ""Future"", ""Future"")",Rule,VALIDATES_WITH,variable_transformations
Alliterations,Variable,"Sortby(""Stdate"")",Rule,VALIDATES_WITH,variable_transformations
Countmap,Variable,Countbystate,Rule,VALIDATES_WITH,variable_transformations
Storydata,Variable,"Typeequals(Almconfig.Storyname) & Notinstate(""Future"")",Rule,VALIDATES_WITH,variable_transformations
Metricstaskdata,Variable,"Typein(Almconfig.Taskname) & Notinstate(""Future"")",Rule,VALIDATES_WITH,variable_transformations
Getstoryprogress,Method,Viewprojectdata,Permission,AUTHORIZES,variable_transformations
Storypoints,Variable,Storypoint,Variable,FLOWS_TO,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Wid,Variable,PRODUCES,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Wid,Variable,USES,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Storypoints,Variable,PRODUCES,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Storypoints,Variable,USES,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Successpercentage,Variable,PRODUCES,variable_transformations
"Storyprogressmodel(string,double,double)",Method,Successpercentage,Variable,USES,variable_transformations
Storypoints,Variable,Storypoint,Variable,TRANSFORMS_TO,variable_transformations
Getwid,Method,Wid,Variable,RETURNS,variable_transformations
Getstorypoint,Method,Storypoints,Variable,RETURNS,variable_transformations
Getstorypoint,Method,Storypoints,Variable,USES,variable_transformations
Setstorypoint,Method,Storypoints,Variable,ACCEPTS,variable_transformations
Setstorypoint,Method,Storypoints,Variable,USES,variable_transformations
Getsuccesspercentage,Method,Successpercentage,Variable,RETURNS,variable_transformations
Getsuccesspercentage,Method,Successpercentage,Variable,USES,variable_transformations
Setsuccesspercentage,Method,Successpercentage,Variable,ACCEPTS,variable_transformations
Getsprintname,Method,Sprintname,Variable,PRODUCES,variable_transformations
Getstorytasks,Method,Storytasks,Variable,PRODUCES,variable_transformations
Getstorytasks,Method,Storytasks,Variable,USES,variable_transformations
Sprintname,Variable,Setsprintname,Method,FLOWS_TO,variable_transformations
Storytasks,Variable,Setstorytasks,Method,FLOWS_TO,variable_transformations
Storyprogresssprintwise,Method,Setsprintname,Method,CALLS,variable_transformations
Storyprogresssprintwise,Method,Setstorytasks,Method,CALLS,variable_transformations
Sprintname,Variable,Getsprintname,Method,FLOWS_TO,variable_transformations
Startdate,Variable,Getstartdate,Method,FLOWS_TO,variable_transformations
Enddate,Variable,Getenddate,Method,FLOWS_TO,variable_transformations
Estimation,Variable,Getestimation,Method,FLOWS_TO,variable_transformations
Completed,Variable,Getcompleted,Method,FLOWS_TO,variable_transformations
Assigneewisetasks,Variable,Getassigneewisetasks,Method,FLOWS_TO,variable_transformations
Assignewisedata,Variable,Getassignewisedata,Method,FLOWS_TO,variable_transformations
Issuecompletionpercentage,Variable,Getissuecompletionpercentage,Method,FLOWS_TO,variable_transformations
Setstartdate,Method,Startdate,Variable,FLOWS_TO,variable_transformations
Setenddate,Method,Enddate,Variable,FLOWS_TO,variable_transformations
Setestimation,Method,Estimation,Variable,FLOWS_TO,variable_transformations
Setcompleted,Method,Completed,Variable,FLOWS_TO,variable_transformations
Setassigneewisetasks,Method,Assigneewisetasks,Variable,FLOWS_TO,variable_transformations
Setassignewisedata,Method,Assignewisedata,Variable,FLOWS_TO,variable_transformations
Setissuecompletionpercentage,Method,Issuecompletionpercentage,Variable,FLOWS_TO,variable_transformations
Getstartdate,Method,Startdate,Variable,PRODUCES,variable_transformations
Getstartdate,Method,Startdate,Variable,USES,variable_transformations
Getenddate,Method,Enddate,Variable,PRODUCES,variable_transformations
Getenddate,Method,Enddate,Variable,USES,variable_transformations
Getestimation,Method,Estimation,Variable,PRODUCES,variable_transformations
Getestimation,Method,Estimation,Variable,USES,variable_transformations
Getcompleted,Method,Completed,Variable,PRODUCES,variable_transformations
Getcompleted,Method,Completed,Variable,USES,variable_transformations
Getassigneewisetasks,Method,Assigneewisetasks,Variable,PRODUCES,variable_transformations
Getassigneewisetasks,Method,Assigneewisetasks,Variable,USES,variable_transformations
Getassignewisedata,Method,Assignewisedata,Variable,PRODUCES,variable_transformations
Getassignewisedata,Method,Assignewisedata,Variable,USES,variable_transformations
Getissuecompletionpercentage,Method,Issuecompletionpercentage,Variable,PRODUCES,variable_transformations
Getissuecompletionpercentage,Method,Issuecompletionpercentage,Variable,USES,variable_transformations
Setsprintname,Method,Sprintname,Variable,CALLS,variable_transformations
Setstartdate,Method,Startdate,Variable,CALLS,variable_transformations
Setenddate,Method,Enddate,Variable,CALLS,variable_transformations
Setestimation,Method,Estimation,Variable,CALLS,variable_transformations
Setcompleted,Method,Completed,Variable,CALLS,variable_transformations
Setassigneewisetasks,Method,Assigneewisetasks,Variable,CALLS,variable_transformations
Setassignewisedata,Method,Assignewisedata,Variable,CALLS,variable_transformations
Setissuecompletionpercentage,Method,Issuecompletionpercentage,Variable,CALLS,variable_transformations
Calclulateabsdiff,Method,Absdiffresult,Variable,PRODUCES,variable_transformations
Calclulateabsdiff,Method,Absdiffresult,Variable,USES,variable_transformations
Calclulateabsdiff,Method,Opnotnull,Rule,VALIDATES_AGAINST,variable_transformations
Calclulateabsdiff,Method,Greaterthancheck,Businessrule,PROCESSES,variable_transformations
Calclulateabsdiff,Method,Lessthancheck,Businessrule,PROCESSES,variable_transformations
Calclulateabsdiff,Method,Equalitycheck,Businessrule,PROCESSES,variable_transformations
Calclulateabsdiff,Method,Greaterthanorequalcheck,Businessrule,PROCESSES,variable_transformations
Calclulateabsdiff,Method,Lessthanorequalcheck,Businessrule,PROCESSES,variable_transformations
Calclulateabsdiff,Method,Absdiffresult,Variable,RETURNS,variable_transformations
Calclulatediff,Method,Diffresult,Variable,PRODUCES,variable_transformations
Calclulatediff,Method,Diffresult,Variable,USES,variable_transformations
Calclulatediff,Method,Opnotnull,Rule,VALIDATES_AGAINST,variable_transformations
Calclulatediff,Method,Greaterthancheck,Businessrule,PROCESSES,variable_transformations
Calclulatediff,Method,Lessthancheck,Businessrule,PROCESSES,variable_transformations
Calclulatediff,Method,Equalitycheck,Businessrule,PROCESSES,variable_transformations
Calclulatediff,Method,Greaterthanorequalcheck,Businessrule,PROCESSES,variable_transformations
Calclulatediff,Method,Lessthanorequalcheck,Businessrule,PROCESSES,variable_transformations
Calclulatediff,Method,Diffresult,Variable,RETURNS,variable_transformations
Calculatepoints,Method,Points,Variable,PRODUCES,variable_transformations
Calculatepoints,Method,Points,Variable,USES,variable_transformations
Calculatepoints,Method,Floor,Method,CALLS,variable_transformations
Calculatepoints,Method,Perfectmatchpoints,Businessrule,PROCESSES,variable_transformations
Calculatepoints,Method,Abovegoalzeropoints,Businessrule,PROCESSES,variable_transformations
Calculatepoints,Method,Proportionalpoints,Businessrule,PROCESSES,variable_transformations
Calculatepoints,Method,Points,Variable,RETURNS,variable_transformations
Calculateabspoints,Method,Abspoints,Variable,PRODUCES,variable_transformations
Calculateabspoints,Method,Absoluteproportionalpoints,Businessrule,PROCESSES,variable_transformations
Calculateabspoints,Method,Abspoints,Variable,RETURNS,variable_transformations
Almconfig,Variable,Velocityfields,Variable,FLOWS_TO,variable_transformations
Almconfig,Variable,Closestates,Variable,FLOWS_TO,variable_transformations
Workingsprints,Variable,Workingbacklog,Variable,FLOWS_TO,variable_transformations
Workingsprints,Variable,Callsp,Method,FLOWS_TO,variable_transformations
Workingsprints,Variable,Scorecardsprintlist,Variable,FLOWS_TO,variable_transformations
Tempsprefined,Variable,Totalsprefined,Variable,FLOWS_TO,variable_transformations
Tempspremoved,Variable,Totalspremoved,Variable,FLOWS_TO,variable_transformations
Workingsprints,Variable,Calcclosedsp,Method,FLOWS_TO,variable_transformations
Issuelist,Variable,Refinedissulist,Variable,FLOWS_TO,variable_transformations
Issuelist,Variable,Removedissulist,Variable,FLOWS_TO,variable_transformations
Refinedissulist,Variable,Refineddefects,Variable,TRANSFORMS_TO,variable_transformations
Scorecardsprintprev,Variable,Callspspillover,Method,FLOWS_TO,variable_transformations
Workitemarr2,Variable,Storyloop,Method,FLOWS_TO,variable_transformations
Scorecardsprint,Variable,Scorecardsprintlist,Variable,FLOWS_TO,variable_transformations
Workingsprints,Variable,Capacitypersprint,Variable,FLOWS_TO,variable_transformations
Calcvelocity,Method,Callsp,Method,CALLS,variable_transformations
Calcvelocity,Method,Calcclosedsp,Method,CALLS,variable_transformations
Calcvelocity,Method,Callspspillover,Method,CALLS,variable_transformations
Calcvelocity,Method,Storylooprefined,Method,CALLS,variable_transformations
Calcvelocity,Method,Filtertrans,Method,CALLS,variable_transformations
Velocitycalculations,Method,Checkremoved,Method,CALLS,variable_transformations
Velocitycalculations,Method,Checkwithdrawn,Method,CALLS,variable_transformations
Callspspillover,Method,Callsp,Method,CALLS,variable_transformations
Storyloop,Method,Storylooprefined,Method,CALLS,variable_transformations
