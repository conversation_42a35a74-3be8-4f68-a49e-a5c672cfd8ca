source_node,source_type,destination_node,destination_type,relationship
LineageTestProject,Folder,AppOne,Folder,CONTAINS
AppOne,Folder,UserService,File,CONTAINS
AppOne,Folder,UserUtils,File,CONTAINS
LineageTestProject,Folder,AppTwo,Folder,CONTAINS
AppTwo,Folder,OrderService,File,CONTAINS
AppTwo,Folder,OrderUtils,File,CONTAINS
Userservice,Class,Createuser,Method,DECLARES
Userservice,Class,Getuser,Method,DECLARES
Userservice,Class,Userrepository,Variable,HAS_FIELD
Createuser,Method,Userrepository,Variable,USES
Getuser,Method,Userrepository,Variable,USES
Userservice,Class,/Create,Endpoint,DECLARES
Userservice,Class,/Get/{Id},Endpoint,DECLARES
Userservice,Class,User,Table,MAPS_TO
Userutils,Class,Convertdtotoentity,Method,DECLARES
Userutils,Class,Generateusercode,Method,DECLARES
Convertdtotoentity,Method,Dto,Variable,USES
Convertdtotoentity,Method,User,Variable,USES
Generateusercode,Method,User,Variable,USES
Generateusercode,Method,Base,Variable,USES
Generateusercode,Method,Code,Variable,USES
Orderservice,Class,Createorder,Method,DECLARES
Orderservice,Class,Orderrepository,Variable,HAS_FIELD
Createorder,Method,Orderrepository,Variable,USES
Orderservice,Class,Order,Table,MAPS_TO
Orderutils,Class,Convertdtotoentity,Method,DECLARES
Orderutils,Class,Calculatetotal,Method,DECLARES
Calculatetotal,Method,Price,Variable,USES
Calculatetotal,Method,Quantity,Variable,USES
Calculatetotal,Method,Total,Variable,USES
Userdto,Variable,User,Variable,TRANSFORMS_TO
User,Variable,Save,Method,FLOWS_TO
Id,Variable,Findbyid,Method,FLOWS_TO
Save,Method,User,Table,WRITES_TO
Findbyid,Method,User,Table,READS_FROM
Userservice,Class,Post:/User/Create,Endpoint,EXPOSES
Userservice,Class,Get:/User/Get/{Id},Endpoint,EXPOSES
Createuser,Method,Post:/User/Create,Endpoint,MAPS_TO
Post:/User/Create,Endpoint,Userdto,Variable,ACCEPTS
Post:/User/Create,Endpoint,User,Variable,RETURNS
Getuser,Method,Get:/User/Get/{Id},Endpoint,MAPS_TO
Get:/User/Get/{Id},Endpoint,Id,Variable,ACCEPTS
Get:/User/Get/{Id},Endpoint,User,Variable,RETURNS
Createuser,Method,Convertdtotoentity,Method,CALLS
Createuser,Method,Save,Method,CALLS
Getuser,Method,Findbyid,Method,CALLS
Name,Variable,Base,Variable,FLOWS_TO
Email,Variable,Base,Variable,FLOWS_TO
Base,Variable,Code,Variable,TRANSFORMS_TO
Convertdtotoentity,Method,User,Variable,PRODUCES
Generateusercode,Method,Code,Variable,PRODUCES
Orderdto,Variable,Order,Variable,TRANSFORMS_TO
User,Variable,Usercode,Variable,TRANSFORMS_TO
"""Pending""",Variable,Status,Variable,FLOWS_TO
Save,Method,Order,Table,WRITES_TO
Orderservice,Class,Post:/Order/Create,Endpoint,EXPOSES
Createorder,Method,Post:/Order/Create,Endpoint,MAPS_TO
Post:/Order/Create,Endpoint,Orderdto,Variable,ACCEPTS
Post:/Order/Create,Endpoint,Order,Variable,RETURNS
Createorder,Method,Convertdtotoentity,Method,CALLS
Createorder,Method,Save,Method,CALLS
Createorder,Method,Getuser,Method,CALLS
Createorder,Method,Appone.Userutils.Generateusercode,Externalservice,INVOKES
Price,Variable,Total,Variable,FLOWS_TO
Quantity,Variable,Total,Variable,FLOWS_TO
Total,Variable,Taxedtotal,Variable,TRANSFORMS_TO
Taxedtotal,Variable,Output,Variable,PRODUCES
