source_node,source_type,destination_node,destination_type,relationship
Lineagetestproject,Folder,Appone,Folder,CONTAINS
Appone,Folder,Userservice,File,CONTAINS
Appone,Folder,Userutils,File,CONTAINS
Lineagetestproject,Folder,Apptwo,Folder,CONTAINS
Apptwo,Folder,Orderservice,File,CONTAINS
Apptwo,Folder,Orderutils,File,CONTAINS
Userservice,File,Userservice,Class,DECLARES
Userutils,File,Userutils,Class,DECLARES
Orderservice,File,Orderservice,Class,DECLARES
Orderutils,File,Orderutils,Class,DECLARES
Userservice,Class,Createuser,Method,DECLARES
Userservice,Class,Getuser,Method,DECLARES
Userservice,Class,Userrepository,Variable,HAS_FIELD
Createuser,Method,Userrepository,Variable,USES
Getuser,Method,Userrepository,Variable,USES
Createuser,Method,User,Table,WRITES_TO
Getuser,Method,User,Table,READS_FROM
Userservice,Class,/create,Endpoint,DECLARES
Userservice,Class,/get/{id},Endpoint,DECLARES
Userutils,Class,Convertdtotoentity,Method,DECLARES
Userutils,Class,Generateusercode,Method,DECLARES
Convertdtotoentity,Method,User,Variable,USES
Convertdtotoentity,Method,Dto,Variable,USES
Generateusercode,Method,User,Variable,USES
Generateusercode,Method,Base,Variable,USES
Generateusercode,Method,Code,Variable,USES
Orderservice,Class,Createorder,Method,DECLARES
Orderservice,Class,Orderrepository,Variable,HAS_FIELD
Createorder,Method,Orderrepository,Variable,USES
Orderservice,Class,Order,Table,MAPS_TO
Orderutils,Class,Convertdtotoentity,Method,DECLARES
Orderutils,Class,Calculatetotal,Method,DECLARES
Calculatetotal,Method,Price,Variable,USES
Calculatetotal,Method,Quantity,Variable,USES
Convertdtotoentity,Method,Order,Variable,USES
Calculatetotal,Method,Total,Variable,USES
Calculatetotal,Method,Taxedtotal,Variable,USES
Userdto,Variable,User,Variable,TRANSFORMS_TO
User,Variable,Createuser,Method,PRODUCES
Save,Method,User,Table,WRITES_TO
Findbyid,Method,User,Table,READS_FROM
Userservice,Class,Post:/User/Create,Endpoint,EXPOSES
Userservice,Class,Get:/User/Get/{Id},Endpoint,EXPOSES
Createuser,Method,Post:/User/Create,Endpoint,MAPS_TO
Getuser,Method,Get:/User/Get/{Id},Endpoint,MAPS_TO
Post:/User/Create,Endpoint,Userdto,Variable,ACCEPTS
Post:/User/Create,Endpoint,User,Variable,RETURNS
Get:/User/Get/{Id},Endpoint,Id,Variable,ACCEPTS
Get:/User/Get/{Id},Endpoint,User,Variable,RETURNS
Createuser,Method,Convertdtotoentity,Method,CALLS
Createuser,Method,Setstatus,Method,CALLS
Createuser,Method,Save,Method,CALLS
Getuser,Method,Findbyid,Method,CALLS
Name,Variable,Base,Variable,FLOWS_TO
Email,Variable,Base,Variable,FLOWS_TO
Base,Variable,Code,Variable,TRANSFORMS_TO
Convertdtotoentity,Method,User,Variable,PRODUCES
Generateusercode,Method,Code,Variable,PRODUCES
Orderdto,Variable,Convertdtotoentity(Orderdto),Variable,FLOWS_TO
Convertdtotoentity(Orderdto),Variable,Order,Variable,TRANSFORMS_TO
Getuser(),Variable,User,Variable,FLOWS_TO
Generateusercode(User),Variable,Usercode,Variable,TRANSFORMS_TO
"""Pending""",Variable,Status,Variable,FLOWS_TO
Save,Method,Order,Table,WRITES_TO
Orderservice,Class,Post:/Order/Create,Endpoint,EXPOSES
Createorder,Method,Post:/Order/Create,Endpoint,MAPS_TO
Post:/Order/Create,Endpoint,Orderdto,Variable,ACCEPTS
Post:/Order/Create,Endpoint,Order,Variable,RETURNS
Createorder,Method,Convertdtotoentity,Method,CALLS
Createorder,Method,Getuser,Method,CALLS
Createorder,Method,Generateusercode,Method,CALLS
Createorder,Method,Save,Method,CALLS
Price,Variable,Total,Variable,FLOWS_TO
Quantity,Variable,Total,Variable,FLOWS_TO
Total,Variable,Taxedtotal,Variable,TRANSFORMS_TO
Taxedtotal,Variable,Output,Variable,PRODUCES
Convertdtotoentity,Method,Order,Variable,PRODUCES
Calculatetotal,Method,Taxedtotal,Variable,PRODUCES
