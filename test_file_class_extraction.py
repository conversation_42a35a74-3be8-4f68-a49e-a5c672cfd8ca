#!/usr/bin/env python3
"""
Quick test to verify file-class relationship extraction is working
"""

import os
from pathlib import Path
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/LineageTestProject")
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

def extract_clean_name(full_name, name_type):
    """Extract clean name from potentially concatenated strings"""
    if not full_name:
        return full_name
    
    # Remove file extensions
    import re
    full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
    
    # Handle file.class patterns - extract only class name
    if '.' in full_name and name_type.lower() in ['class', 'interface']:
        parts = full_name.split('.')
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Apply PascalCase for classes, methods, files, folders
    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:
        return to_pascal_case(full_name)
    
    return full_name

def to_pascal_case(text):
    """Convert text to PascalCase"""
    if not text:
        return text
    
    import re
    # Split on common delimiters and capitalize each part
    parts = re.split(r'[_\-\s]+', text)
    result = ''
    for part in parts:
        if part:
            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()
    
    return result if result else text

def test_file_class_extraction():
    """Test file-class relationship extraction"""
    print("🧪 Testing File-Class Relationship Extraction")
    print(f"📁 Base path: {BASE_PATH}")
    
    file_class_relationships = []
    
    # Walk through all Java files
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                print(f"\n📄 Processing: {file}")
                
                try:
                    # Read and parse the Java file
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    print(f"   📝 File content length: {len(source_code_str)} characters")
                    
                    source_code = source_code_str.encode('utf-8')
                    tree = parser.parse(source_code)
                    root_node = tree.root_node
                    
                    # Extract class names from AST
                    def find_classes(node):
                        classes = []
                        if node.type in ['class_declaration', 'interface_declaration', 'enum_declaration']:
                            for child in node.children:
                                if child.type == 'identifier':
                                    class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                    classes.append(extract_clean_name(class_name, 'class'))
                                    break
                        
                        for child in node.children:
                            classes.extend(find_classes(child))
                        
                        return classes
                    
                    classes_in_file = find_classes(root_node)
                    print(f"   🏛️ Classes found: {classes_in_file}")
                    
                    # Create file-class relationships
                    file_name = extract_clean_name(file.replace('.java', ''), 'file')
                    for class_name in classes_in_file:
                        relationship = {
                            'source_node': file_name,
                            'source_type': 'File',
                            'destination_node': class_name,
                            'destination_type': 'Class',
                            'relationship': 'DECLARES',
                            'file_path': file_path
                        }
                        file_class_relationships.append(relationship)
                        print(f"   ✅ Created: {file_name} -[DECLARES]-> {class_name}")
                        
                except Exception as e:
                    print(f"   ❌ Error processing {file_path}: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Total file-class relationships: {len(file_class_relationships)}")
    
    if file_class_relationships:
        print(f"\n📋 All relationships:")
        for rel in file_class_relationships:
            print(f"   {rel['source_node']} -[{rel['relationship']}]-> {rel['destination_node']}")

        # Save to simple text file for inspection
        with open('test_file_class_relationships.txt', 'w') as f:
            for rel in file_class_relationships:
                f.write(f"{rel['source_node']},{rel['source_type']},{rel['destination_node']},{rel['destination_type']},{rel['relationship']}\n")
        print(f"\n💾 Saved to: test_file_class_relationships.txt")

        return file_class_relationships
    else:
        print("   ❌ No relationships found!")
        return []

if __name__ == "__main__":
    test_file_class_extraction()
